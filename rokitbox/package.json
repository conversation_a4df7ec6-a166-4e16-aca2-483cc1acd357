{"name": "rokitbox", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@headlessui/react": "^2.2.8", "@heroicons/react": "^2.2.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.16.2", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^7.0.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "15.5.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.16.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5"}}