var C=Object.defineProperty;var c=(t,e)=>C(t,"name",{value:e,configurable:!0});import{isMainThread as $}from"node:worker_threads";import{i as y,a as q,e as B,m as G}from"../node-features-_8ZFwP_x.mjs";import{r as H}from"../register-B7jrtLTO.mjs";import"../get-pipe-path-BHW2eJdv.mjs";import"node:module";import l from"node:path";import{fileURLToPath as k,pathToFileURL as E}from"node:url";import"get-tsconfig";import{l as T,t as Q,b as X,d as m,f,e as w,g as S,h as _,j as L,k as b,m as P,n as j,o as z,p as F,q as J}from"../register-CFH5oNdT.mjs";import N from"node:fs";import"esbuild";import"node:crypto";import{i as K,a as V,t as Y,b as Z,r as x}from"../index-7AaEi15b.mjs";import{p as O}from"../client-BQVF1NaW.mjs";import"../require-DQxpCAr4.mjs";import{readFile as tt}from"node:fs/promises";import"module";import"../temporary-directory-CwHp0_NW.mjs";import"node:os";import"node:util";import"../index-gbaejti9.mjs";import"node:net";const p={active:!0},et=c(async t=>{if(!t)throw new Error(`tsx must be loaded with --import instead of --loader
The --loader flag was deprecated in Node v20.6.0 and v18.19.0`);p.namespace=t.namespace,t.tsconfig!==!1&&T(t.tsconfig??process.env.TSX_TSCONFIG_PATH),t.port&&(p.port=t.port,t.port.on("message",e=>{e==="deactivate"&&(p.active=!1,t.port.postMessage({type:"deactivated"}))}))},"initialize"),at=c(()=>(T(process.env.TSX_TSCONFIG_PATH),"process.setSourceMapsEnabled(true);"),"globalPreload"),u=new Map,st=c(async t=>{if(u.has(t))return u.get(t);if(!await N.promises.access(t).then(()=>!0,()=>!1)){u.set(t,void 0);return}const r=await N.promises.readFile(t,"utf8");try{const a=JSON.parse(r);return u.set(t,a),a}catch{throw new Error(`Error parsing: ${t}`)}},"readPackageJson"),rt=c(async t=>{let e=new URL("package.json",t);for(;!e.pathname.endsWith("/node_modules/package.json");){const r=k(e),a=await st(r);if(a)return a;const s=e;if(e=new URL("../package.json",e),e.pathname===s.pathname)break}},"findPackageJson"),ot=c(async t=>(await rt(t))?.type??"commonjs","getPackageType"),nt=c(t=>{const{pathname:e}=new URL(t),r=l.extname(e);if(r===".mts"||r===".mjs")return"module";if(r===".cts"||r===".cjs")return"commonjs";if(r===".js"||Q.includes(r))return ot(t)},"getFormatFromFileUrl"),h="tsx-namespace=",R=c(t=>{const e=t.indexOf(h);if(e===-1)return;const r=t[e-1];if(r!=="?"&&r!=="&")return;const a=e+h.length,s=t.indexOf("&",a);return s===-1?t.slice(a):t.slice(a,s)},"getNamespace"),W=y(q)?"importAttributes":"importAssertions";let U=c(async(t,e,r)=>{if(!p.active)return r(t,e);const a=R(t);if(p.namespace&&p.namespace!==a)return r(t,e);if(p.port){const o=new URL(t);o.searchParams.delete("tsx-namespace"),p.port.postMessage({type:"load",url:o.toString()})}if(O.send&&O.send({type:"dependency",path:t}),X.test(t)){let o=e[W];o||(o={},e[W]=o),o.type||(o.type="json")}const s=await r(t,e);m(3,"loaded by next loader",{url:t,loaded:s});const i=t.startsWith(f)?k(t):t;if(s.format==="commonjs"&&y(B)&&s.responseURL?.startsWith("file:")&&!i.endsWith(".cjs")){const o=await tt(new URL(t),"utf8");if(!i.endsWith(".js")||K(o)){const d=V(o,i,{tsconfigRaw:S?.(i)}),I=a?`${i}?namespace=${encodeURIComponent(a)}`:i;return s.responseURL=`data:text/javascript,${encodeURIComponent(d.code)}?filePath=${encodeURIComponent(I)}`,m(3,"returning CJS export annotation",s),s}}if(!s.source)return s;const n=s.source.toString();if(s.format==="json"||w.test(t)){const o=await Y(n,i,{tsconfigRaw:l.isAbsolute(i)?S?.(i):void 0});return{format:"module",source:_(o)}}if(s.format==="module"){const o=Z(i,n);o&&(s.source=_(o))}return s},"load");if(L){const t=U;U=c(async(e,r,a)=>{m(2,"load",{url:e,context:r});const s=await t(e,r,a);return m(1,"loaded",{url:e,result:s}),s},"load")}const A=c(t=>{if(t.url)return t.url;const e=t.message.match(/^Cannot find module '([^']+)'/);if(e){const[,a]=e;return a}const r=t.message.match(/^Cannot find package '([^']+)'/);if(r){const[,a]=r;if(!l.isAbsolute(a))return;const s=E(a);if(s.pathname.endsWith("/")&&(s.pathname+="package.json"),s.pathname.endsWith("/package.json")){const i=x(s);if(i?.main)return new URL(i.main,s).toString()}else return s.toString()}},"getMissingPathFromNotFound"),g=c(async(t,e,r,a)=>{const s=z(t);if(m(3,"resolveExtensions",{url:t,context:e,throwError:a,tryPaths:s}),!s)return;let i;for(const n of s)try{return await r(n,e)}catch(o){const{code:d}=o;if(d!=="ERR_MODULE_NOT_FOUND"&&d!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw o;i=o}if(a)throw i},"resolveExtensions"),it=c(async(t,e,r)=>{if(m(3,"resolveBase",{specifier:t,context:e,specifierStartsWithFileUrl:t.startsWith(f),isRelativePath:F(t),tsExtensionsPattern:w.test(e.parentURL),allowJs:J}),(t.startsWith(f)||F(t))&&(w.test(e.parentURL)||J)){const a=await g(t,e,r);if(m(3,"resolveBase resolved",{specifier:t,context:e,resolved:a}),a)return a}try{return await r(t,e)}catch(a){if(m(3,"resolveBase error",{specifier:t,context:e,error:a}),a instanceof Error){const s=a;if(s.code==="ERR_MODULE_NOT_FOUND"){const i=A(s);if(i){const n=await g(i,e,r);if(n)return n}}}throw a}},"resolveBase"),M=c(async(t,e,r)=>{if(m(3,"resolveDirectory",{specifier:t,context:e,isDirectory:j.test(t)}),(t==="."||t===".."||t.endsWith("/.."))&&(t+="/"),j.test(t)){const a=new URL(t,e.parentURL);return a.pathname=l.join(a.pathname,"index"),await g(a.toString(),e,r,!0)}try{return await it(t,e,r)}catch(a){if(a instanceof Error){m(3,"resolveDirectory error",{specifier:t,context:e,error:a});const s=a;if(s.code==="ERR_UNSUPPORTED_DIR_IMPORT"){const i=A(s);if(i)try{return await g(`${i}/index`,e,r,!0)}catch(n){const o=n,{message:d}=o;throw o.message=o.message.replace(`${"/index".replace("/",l.sep)}'`,"'"),o.stack=o.stack.replace(d,o.message),o}}}throw a}},"resolveDirectory"),ct=c(async(t,e,r)=>{if(m(3,"resolveTsPaths",{specifier:t,context:e,requestAcceptsQuery:b(t),tsconfigPathsMatcher:P,fromNodeModules:e.parentURL?.includes("/node_modules/")}),!b(t)&&P&&!e.parentURL?.includes("/node_modules/")){const a=P(t);m(3,"resolveTsPaths",{possiblePaths:a});for(const s of a)try{return await M(E(s).toString(),e,r)}catch{}}return M(t,e,r)},"resolveTsPaths"),D="tsx://";let v=c(async(t,e,r)=>{if(!p.active||t.startsWith("node:"))return r(t,e);let a=R(t)??(e.parentURL&&R(e.parentURL));if(p.namespace){let o;if(t.startsWith(D)){try{o=JSON.parse(t.slice(D.length))}catch{}o?.namespace&&(a=o.namespace)}if(p.namespace!==a)return r(t,e);o&&(t=o.specifier,e.parentURL=o.parentURL)}const[s,i]=t.split("?"),n=await ct(s,e,r);return m(2,"nextResolve",{resolved:n}),n.format==="builtin"||((!n.format||n.format==="commonjs-typescript"||n.format==="module-typescript")&&n.url.startsWith(f)&&(n.format=await nt(n.url),m(2,"getFormatFromFileUrl",{resolved:n,format:n.format})),i&&(n.url+=`?${i}`),a&&!n.url.includes(h)&&(n.url+=(n.url.includes("?")?"&":"?")+h+a)),n},"resolve");if(L){const t=v;v=c(async(e,r,a)=>{m(2,"resolve",{specifier:e,context:r});const s=await t(e,r,a);return m(1,"resolved",{specifier:e,context:r,result:s}),s},"resolve")}y(G)&&$&&H();export{at as globalPreload,et as initialize,U as load,v as resolve};
