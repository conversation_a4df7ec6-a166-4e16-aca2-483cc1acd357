{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/Documents_Tech_augment-projects_RokitBox_rokitbox_2f18907d._.js", "server/edge/chunks/[root-of-the-server]__8195f8a6._.js", "server/edge/chunks/358b3_Tech_augment-projects_RokitBox_rokitbox_edge-wrapper_d4062bb7.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.png$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.png$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "2Rz+Dp7cwpHr1g3oC/A3lIbmRroTb6zOhctc1mOEeME=", "__NEXT_PREVIEW_MODE_ID": "2efaed95c387efc0ca01e39a4f5d4864", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a0417f3a6620ea7630820ece5196cfa2c973fb24c561d62be4cf1b1231a38cfb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3e1fef9f04651676a9a0d0421dcdb34bc9edb36a2ee40c39f51456623b615455"}}}, "sortedMiddleware": ["/"], "functions": {}}