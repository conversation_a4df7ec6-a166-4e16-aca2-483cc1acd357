{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/Documents_Tech_augment-projects_RokitBox_rokitbox_2f18907d._.js", "server/edge/chunks/[root-of-the-server]__8195f8a6._.js", "server/edge/chunks/358b3_Tech_augment-projects_RokitBox_rokitbox_edge-wrapper_d4062bb7.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "2Rz+Dp7cwpHr1g3oC/A3lIbmRroTb6zOhctc1mOEeME=", "__NEXT_PREVIEW_MODE_ID": "a935b5afb0426b286e86ec0f0b612b61", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "295c0758a0f36eddf2c5c726fc63b2c2538fbffb86ccfec9cbb09be48700e756", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "669aaef40c4ec4634726b6c1b433e57d51ee88175ce43aed87f098cf94a57b9f"}}}, "sortedMiddleware": ["/"], "functions": {}}