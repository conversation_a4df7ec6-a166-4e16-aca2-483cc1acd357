{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/middleware.ts"], "sourcesContent": ["import { withAuth } from \"next-auth/middleware\"\n\nexport default withAuth(\n  function middleware(req) {\n    // Middleware logic here if needed\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Allow access to auth pages without token\n        if (req.nextUrl.pathname.startsWith(\"/auth\")) {\n          return true\n        }\n        \n        // Require token for all other protected routes\n        return !!token\n      },\n    },\n  }\n)\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api/auth (NextAuth.js routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    \"/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)\",\n  ],\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;uCAEe,IAAA,kOAAQ,EACrB,SAAS,WAAW,GAAG;AACrB,kCAAkC;AACpC,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,2CAA2C;YAC3C,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;gBAC5C,OAAO;YACT;YAEA,+CAA+C;YAC/C,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}