{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/middleware.ts"], "sourcesContent": ["import { withAuth } from \"next-auth/middleware\"\n\nexport default withAuth(\n  function middleware(req) {\n    // Middleware logic here if needed\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        const { pathname } = req.nextUrl\n\n        // Allow access to auth pages without token\n        if (pathname.startsWith(\"/auth\")) {\n          return true\n        }\n\n        // Allow access to API auth routes\n        if (pathname.startsWith(\"/api/auth\")) {\n          return true\n        }\n\n        // Require token for all other protected routes\n        return !!token\n      },\n    },\n  }\n)\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api/auth (NextAuth.js routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    \"/((?!_next/static|_next/image|favicon.ico|.*\\\\.png$).*)\",\n  ],\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;uCAEe,IAAA,kOAAQ,EACrB,SAAS,WAAW,GAAG;AACrB,kCAAkC;AACpC,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;YAEhC,2CAA2C;YAC3C,IAAI,SAAS,UAAU,CAAC,UAAU;gBAChC,OAAO;YACT;YAEA,kCAAkC;YAClC,IAAI,SAAS,UAAU,CAAC,cAAc;gBACpC,OAAO;YACT;YAEA,+CAA+C;YAC/C,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}