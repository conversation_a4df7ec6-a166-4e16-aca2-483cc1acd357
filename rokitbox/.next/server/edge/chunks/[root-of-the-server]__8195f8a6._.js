(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push(["chunks/[root-of-the-server]__8195f8a6._.js",
"[externals]/node:buffer [external] (node:buffer, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}),
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/middleware.ts [middleware-edge] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "config",
    ()=>config,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$Tech$2f$augment$2d$projects$2f$RokitBox$2f$rokitbox$2f$node_modules$2f$next$2d$auth$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next-auth/middleware.js [middleware-edge] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$Tech$2f$augment$2d$projects$2f$RokitBox$2f$rokitbox$2f$node_modules$2f$next$2d$auth$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["withAuth"])(function middleware(req) {
// Middleware logic here if needed
}, {
    callbacks: {
        authorized: ({ token, req })=>{
            const { pathname } = req.nextUrl;
            // Allow access to auth pages without token
            if (pathname.startsWith("/auth")) {
                return true;
            }
            // Allow access to API auth routes
            if (pathname.startsWith("/api/auth")) {
                return true;
            }
            // Require token for all other protected routes
            return !!token;
        }
    }
});
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */ "/((?!_next/static|_next/image|favicon.ico|.*\\.png$).*)"
    ]
};
}),
]);

//# sourceMappingURL=%5Broot-of-the-server%5D__8195f8a6._.js.map