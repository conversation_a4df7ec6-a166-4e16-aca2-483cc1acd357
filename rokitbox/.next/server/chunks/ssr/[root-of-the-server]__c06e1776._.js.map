{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,0OAAO,EAAC,IAAA,iNAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/src/components/ui/Card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-secondary-200 bg-white text-secondary-950 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-secondary-500\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,uRAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kTAAC;QACC,KAAK;QACL,WAAW,IAAA,6LAAE,EACX,gFACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,uRAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kTAAC;QACC,KAAK;QACL,WAAW,IAAA,6LAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,uRAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kTAAC;QACC,KAAK;QACL,WAAW,IAAA,6LAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,uRAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kTAAC;QACC,KAAK;QACL,WAAW,IAAA,6LAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,uRAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kTAAC;QAAI,KAAK;QAAK,WAAW,IAAA,6LAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,uRAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kTAAC;QACC,KAAK;QACL,WAAW,IAAA,6LAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/src/components/ui/Input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-secondary-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-rokit-orange focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,uRAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,kTAAC;QACC,MAAM;QACN,WAAW,IAAA,6LAAE,EACX,mWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-rokit-orange text-white hover:bg-primary-600',\n        destructive: 'bg-red-500 text-white hover:bg-red-600',\n        outline: 'border border-rokit-orange text-rokit-orange hover:bg-rokit-orange hover:text-white',\n        secondary: 'bg-secondary-100 text-secondary-900 hover:bg-secondary-200',\n        ghost: 'hover:bg-secondary-100 hover:text-secondary-900',\n        link: 'underline-offset-4 hover:underline text-rokit-orange',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size }), className)}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,IAAA,2OAAG,EACxB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oRAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,kTAAC;QACC,WAAW,IAAA,6LAAE,EAAC,eAAe;YAAE;YAAS;QAAK,IAAI;QACjD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/src/components/ui/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LogoProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n}\n\nconst Logo: React.FC<LogoProps> = ({ className = '', size = 'md' }) => {\n  const sizeClasses = {\n    sm: 'h-8',\n    md: 'h-12',\n    lg: 'h-16',\n    xl: 'h-24'\n  };\n\n  return (\n    <div className={`flex items-center ${className}`}>\n      {/* RokitBox Logo SVG based on the provided image */}\n      <svg \n        className={`${sizeClasses[size]} w-auto`}\n        viewBox=\"0 0 400 120\" \n        fill=\"none\" \n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        {/* Main text \"RokIT\" */}\n        <text \n          x=\"10\" \n          y=\"70\" \n          className=\"fill-rokit-black\" \n          style={{ \n            fontSize: '48px', \n            fontWeight: 'bold', \n            fontFamily: 'Arial, sans-serif' \n          }}\n        >\n          Rok\n        </text>\n        \n        {/* Orange \"IT\" part with checkmark */}\n        <text \n          x=\"140\" \n          y=\"70\" \n          className=\"fill-rokit-orange\" \n          style={{ \n            fontSize: '48px', \n            fontWeight: 'bold', \n            fontFamily: 'Arial, sans-serif' \n          }}\n        >\n          IT\n        </text>\n        \n        {/* Orange checkmark/arrow */}\n        <path \n          d=\"M220 20 L280 20 L320 60 L280 100 L260 80 L290 50 L260 20 Z\" \n          className=\"fill-rokit-orange\"\n        />\n        \n        {/* Subtitle \"Partner s.r.o.\" */}\n        <text \n          x=\"10\" \n          y=\"95\" \n          className=\"fill-rokit-black\" \n          style={{ \n            fontSize: '16px', \n            fontWeight: 'normal', \n            fontFamily: 'Arial, sans-serif' \n          }}\n        >\n          Partner s.r.o.\n        </text>\n      </svg>\n    </div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;;;AAOA,MAAM,OAA4B,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,IAAI,EAAE;IAChE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,kTAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;kBAE9C,cAAA,kTAAC;YACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC;YACxC,SAAQ;YACR,MAAK;YACL,OAAM;;8BAGN,kTAAC;oBACC,GAAE;oBACF,GAAE;oBACF,WAAU;oBACV,OAAO;wBACL,UAAU;wBACV,YAAY;wBACZ,YAAY;oBACd;8BACD;;;;;;8BAKD,kTAAC;oBACC,GAAE;oBACF,GAAE;oBACF,WAAU;oBACV,OAAO;wBACL,UAAU;wBACV,YAAY;wBACZ,YAAY;oBACd;8BACD;;;;;;8BAKD,kTAAC;oBACC,GAAE;oBACF,WAAU;;;;;;8BAIZ,kTAAC;oBACC,GAAE;oBACF,GAAE;oBACF,WAAU;oBACV,OAAO;wBACL,UAAU;wBACV,YAAY;wBACZ,YAAY;oBACd;8BACD;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { signIn, getSession } from \"next-auth/react\"\nimport { useRouter } from \"next/navigation\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/Card\"\nimport { Input } from \"@/components/ui/Input\"\nimport { Button } from \"@/components/ui/Button\"\nimport Logo from \"@/components/ui/Logo\"\n\nexport default function SignIn() {\n  const [email, setEmail] = useState(\"\")\n  const [password, setPassword] = useState(\"\")\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState(\"\")\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError(\"\")\n\n    try {\n      const result = await signIn(\"credentials\", {\n        email,\n        password,\n        redirect: false,\n      })\n\n      if (result?.error) {\n        setError(\"Neplatn<PERSON> p<PERSON>í údaje\")\n      } else {\n        // Refresh session and redirect\n        await getSession()\n        router.push(\"/dashboard\")\n        router.refresh()\n      }\n    } catch (error) {\n      setError(\"Došlo k chybě při přihlašování\")\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-secondary-50 to-primary-50 px-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"space-y-1 text-center\">\n          <div className=\"flex justify-center mb-4\">\n            <Logo size=\"lg\" />\n          </div>\n          <CardTitle className=\"text-2xl font-bold\">Přihlášení</CardTitle>\n          <CardDescription>\n            Zadejte své přihlašovací údaje pro vstup do systému\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <label htmlFor=\"email\" className=\"text-sm font-medium text-secondary-700\">\n                Email\n              </label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                placeholder=\"<EMAIL>\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <label htmlFor=\"password\" className=\"text-sm font-medium text-secondary-700\">\n                Heslo\n              </label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                placeholder=\"••••••••\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            {error && (\n              <div className=\"text-red-600 text-sm text-center\">\n                {error}\n              </div>\n            )}\n            <Button\n              type=\"submit\"\n              className=\"w-full\"\n              disabled={isLoading}\n            >\n              {isLoading ? \"Přihlašování...\" : \"Přihlásit se\"}\n            </Button>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,qRAAQ,EAAC;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,qRAAQ,EAAC;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,qRAAQ,EAAC;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,qRAAQ,EAAC;IACnC,MAAM,SAAS,IAAA,mNAAS;IAExB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,IAAA,4NAAM,EAAC,eAAe;gBACzC;gBACA;gBACA,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;YACX,OAAO;gBACL,+BAA+B;gBAC/B,MAAM,IAAA,gOAAU;gBAChB,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,kTAAC;QAAI,WAAU;kBACb,cAAA,kTAAC,4MAAI;YAAC,WAAU;;8BACd,kTAAC,kNAAU;oBAAC,WAAU;;sCACpB,kTAAC;4BAAI,WAAU;sCACb,cAAA,kTAAC,+MAAI;gCAAC,MAAK;;;;;;;;;;;sCAEb,kTAAC,iNAAS;4BAAC,WAAU;sCAAqB;;;;;;sCAC1C,kTAAC,uNAAe;sCAAC;;;;;;;;;;;;8BAInB,kTAAC,mNAAW;8BACV,cAAA,kTAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,kTAAC;gCAAI,WAAU;;kDACb,kTAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAAyC;;;;;;kDAG1E,kTAAC,8MAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,QAAQ;wCACR,UAAU;;;;;;;;;;;;0CAGd,kTAAC;gCAAI,WAAU;;kDACb,kTAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAAyC;;;;;;kDAG7E,kTAAC,8MAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,QAAQ;wCACR,UAAU;;;;;;;;;;;;4BAGb,uBACC,kTAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,kTAAC,gNAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU;0CAET,YAAY,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}