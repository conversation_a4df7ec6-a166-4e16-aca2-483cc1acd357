module.exports = [
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/.next-internal/server/app/auth/signin/page/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico.mjs { IMAGE => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico.mjs { IMAGE => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}),
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/layout.tsx [app-rsc] (ecmascript)"));
}),
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx [app-rsc] (client reference proxy) <module evaluation>", ((__turbopack_context__) => {
"use strict";

// This file is generated by next-core EcmascriptClientReferenceModule.
__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$Tech$2f$augment$2d$projects$2f$RokitBox$2f$rokitbox$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$Tech$2f$augment$2d$projects$2f$RokitBox$2f$rokitbox$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx <module evaluation>", "default");
}),
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx [app-rsc] (client reference proxy)", ((__turbopack_context__) => {
"use strict";

// This file is generated by next-core EcmascriptClientReferenceModule.
__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$Tech$2f$augment$2d$projects$2f$RokitBox$2f$rokitbox$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$Tech$2f$augment$2d$projects$2f$RokitBox$2f$rokitbox$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx", "default");
}),
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$Tech$2f$augment$2d$projects$2f$RokitBox$2f$rokitbox$2f$src$2f$app$2f$auth$2f$signin$2f$page$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$Tech$2f$augment$2d$projects$2f$RokitBox$2f$rokitbox$2f$src$2f$app$2f$auth$2f$signin$2f$page$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$Tech$2f$augment$2d$projects$2f$RokitBox$2f$rokitbox$2f$src$2f$app$2f$auth$2f$signin$2f$page$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx [app-rsc] (ecmascript)"));
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__d4ab1042._.js.map