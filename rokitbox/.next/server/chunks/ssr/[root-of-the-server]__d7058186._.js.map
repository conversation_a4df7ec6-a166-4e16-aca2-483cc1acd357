{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation'\n\nexport default function Home() {\n  redirect('/dashboard')\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEe,SAAS;IACtB,IAAA,qQAAQ,EAAC;AACX", "debugId": null}}]}