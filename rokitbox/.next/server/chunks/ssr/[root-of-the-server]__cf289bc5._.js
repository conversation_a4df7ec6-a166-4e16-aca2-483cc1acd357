module.exports = [
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/.next-internal/server/app/_not-found/page/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico.mjs { IMAGE => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico.mjs { IMAGE => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}),
"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/layout.tsx [app-rsc] (ecmascript)"));
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__cf289bc5._.js.map