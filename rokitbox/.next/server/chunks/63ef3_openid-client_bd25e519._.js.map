{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/errors.js"], "sourcesContent": ["const { format } = require('util');\n\nclass OPError extends Error {\n  constructor({ error_description, error, error_uri, session_state, state, scope }, response) {\n    super(!error_description ? error : `${error} (${error_description})`);\n\n    Object.assign(\n      this,\n      { error },\n      error_description && { error_description },\n      error_uri && { error_uri },\n      state && { state },\n      scope && { scope },\n      session_state && { session_state },\n    );\n\n    if (response) {\n      Object.defineProperty(this, 'response', {\n        value: response,\n      });\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nclass RPError extends Error {\n  constructor(...args) {\n    if (typeof args[0] === 'string') {\n      super(format(...args));\n    } else {\n      const { message, printf, response, ...rest } = args[0];\n      if (printf) {\n        super(format(...printf));\n      } else {\n        super(message);\n      }\n      Object.assign(this, rest);\n      if (response) {\n        Object.defineProperty(this, 'response', {\n          value: response,\n        });\n      }\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nmodule.exports = {\n  OPError,\n  RPError,\n};\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,MAAM,EAAE;AAEhB,MAAM,gBAAgB;IACpB,YAAY,EAAE,iBAAiB,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,QAAQ,CAAE;QAC1F,KAAK,CAAC,CAAC,oBAAoB,QAAQ,GAAG,MAAM,EAAE,EAAE,kBAAkB,CAAC,CAAC;QAEpE,OAAO,MAAM,CACX,IAAI,EACJ;YAAE;QAAM,GACR,qBAAqB;YAAE;QAAkB,GACzC,aAAa;YAAE;QAAU,GACzB,SAAS;YAAE;QAAM,GACjB,SAAS;YAAE;QAAM,GACjB,iBAAiB;YAAE;QAAc;QAGnC,IAAI,UAAU;YACZ,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;gBACtC,OAAO;YACT;QACF;QAEA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAChD;AACF;AAEA,MAAM,gBAAgB;IACpB,YAAY,GAAG,IAAI,CAAE;QACnB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;YAC/B,KAAK,CAAC,UAAU;QAClB,OAAO;YACL,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,EAAE;YACtD,IAAI,QAAQ;gBACV,KAAK,CAAC,UAAU;YAClB,OAAO;gBACL,KAAK,CAAC;YACR;YACA,OAAO,MAAM,CAAC,IAAI,EAAE;YACpB,IAAI,UAAU;gBACZ,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;oBACtC,OAAO;gBACT;YACF;QACF;QAEA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAChD;AACF;AAEA,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/is_key_object.js"], "sourcesContent": ["const util = require('util');\nconst crypto = require('crypto');\n\nmodule.exports = util.types.isKeyObject || ((obj) => obj && obj instanceof crypto.KeyObject);\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AAEN,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,MAAQ,OAAO,eAAe,OAAO,SAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/base64url.js"], "sourcesContent": ["let encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = (input, encoding = 'utf8') => Buffer.from(input, encoding).toString('base64url');\n} else {\n  const fromBase64 = (base64) => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = (input, encoding = 'utf8') =>\n    fromBase64(Buffer.from(input, encoding).toString('base64'));\n}\n\nconst decode = (input) => Buffer.from(input, 'base64');\n\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI,OAAO,UAAU,CAAC,cAAc;IAClC,SAAS,CAAC,OAAO,WAAW,MAAM,GAAK,OAAO,IAAI,CAAC,OAAO,UAAU,QAAQ,CAAC;AAC/E,OAAO;IACL,MAAM,aAAa,CAAC,SAAW,OAAO,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;IAC3F,SAAS,CAAC,OAAO,WAAW,MAAM,GAChC,WAAW,OAAO,IAAI,CAAC,OAAO,UAAU,QAAQ,CAAC;AACrD;AAEA,MAAM,SAAS,CAAC,QAAU,OAAO,IAAI,CAAC,OAAO;AAE7C,OAAO,OAAO,CAAC,MAAM,GAAG;AACxB,OAAO,OAAO,CAAC,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/decode_jwt.js"], "sourcesContent": ["const base64url = require('./base64url');\n\nmodule.exports = (token) => {\n  if (typeof token !== 'string' || !token) {\n    throw new TypeError('JWT must be a string');\n  }\n\n  const { 0: header, 1: payload, 2: signature, length } = token.split('.');\n\n  if (length === 5) {\n    throw new TypeError('encrypted JWTs cannot be decoded');\n  }\n\n  if (length !== 3) {\n    throw new Error('JWTs must have three components');\n  }\n\n  try {\n    return {\n      header: JSON.parse(base64url.decode(header)),\n      payload: JSON.parse(base64url.decode(payload)),\n      signature,\n    };\n  } catch (err) {\n    throw new Error('JWT is malformed');\n  }\n};\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,OAAO,OAAO,GAAG,CAAC;IAChB,IAAI,OAAO,UAAU,YAAY,CAAC,OAAO;QACvC,MAAM,IAAI,UAAU;IACtB;IAEA,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,SAAS,EAAE,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC;IAEpE,IAAI,WAAW,GAAG;QAChB,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,WAAW,GAAG;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,OAAO;YACL,QAAQ,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC;YACpC,SAAS,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC;YACrC;QACF;IACF,EAAE,OAAO,KAAK;QACZ,MAAM,IAAI,MAAM;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/is_plain_object.js"], "sourcesContent": ["module.exports = (a) => !!a && a.constructor === Object;\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,CAAC,IAAM,CAAC,CAAC,KAAK,EAAE,WAAW,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/defaults.js"], "sourcesContent": ["const isPlainObject = require('./is_plain_object');\n\nfunction defaults(deep, target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (typeof target[key] === 'undefined' && typeof value !== 'undefined') {\n        target[key] = value;\n      }\n\n      if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n        defaults(true, target[key], value);\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,SAAS,SAAS,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO;IACxC,KAAK,MAAM,UAAU,QAAS;QAC5B,IAAI,CAAC,cAAc,SAAS;YAC1B;QACF;QACA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;YACjD,sBAAsB,GACtB,IAAI,QAAQ,eAAe,QAAQ,eAAe;gBAChD;YACF;YACA,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,eAAe,OAAO,UAAU,aAAa;gBACtE,MAAM,CAAC,IAAI,GAAG;YAChB;YAEA,IAAI,QAAQ,cAAc,MAAM,CAAC,IAAI,KAAK,cAAc,QAAQ;gBAC9D,SAAS,MAAM,MAAM,CAAC,IAAI,EAAE;YAC9B;QACF;IACF;IAEA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG,SAAS,IAAI,CAAC,WAAW;AAC1C,OAAO,OAAO,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/www_authenticate_parser.js"], "sourcesContent": ["const REGEXP = /(\\w+)=(\"[^\"]*\")/g;\n\nmodule.exports = (wwwAuthenticate) => {\n  const params = {};\n  try {\n    while (REGEXP.exec(wwwAuthenticate) !== null) {\n      if (RegExp.$1 && RegExp.$2) {\n        params[RegExp.$1] = RegExp.$2.slice(1, -1);\n      }\n    }\n  } catch (err) {}\n\n  return params;\n};\n"], "names": [], "mappings": "AAAA,MAAM,SAAS;AAEf,OAAO,OAAO,GAAG,CAAC;IAChB,MAAM,SAAS,CAAC;IAChB,IAAI;QACF,MAAO,OAAO,IAAI,CAAC,qBAAqB,KAAM;YAC5C,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,EAAE;gBAC1B,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;YAC1C;QACF;IACF,EAAE,OAAO,KAAK,CAAC;IAEf,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/assert.js"], "sourcesContent": ["function assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n  if (!issuer[`${endpoint}_endpoint`]) return;\n\n  const eam = `${endpoint}_endpoint_auth_method`;\n  const easa = `${endpoint}_endpoint_auth_signing_alg`;\n  const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n\n  if (properties[eam] && properties[eam].endsWith('_jwt') && !properties[easa] && !issuer[easavs]) {\n    throw new TypeError(\n      `${easavs} must be configured on the issuer if ${easa} is not defined on a client`,\n    );\n  }\n}\n\nfunction assertIssuerConfiguration(issuer, endpoint) {\n  if (!issuer[endpoint]) {\n    throw new TypeError(`${endpoint} must be configured on the issuer`);\n  }\n}\n\nmodule.exports = {\n  assertSigningAlgValuesSupport,\n  assertIssuerConfiguration,\n};\n"], "names": [], "mappings": "AAAA,SAAS,8BAA8B,QAAQ,EAAE,MAAM,EAAE,UAAU;IACjE,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE;IAErC,MAAM,MAAM,GAAG,SAAS,qBAAqB,CAAC;IAC9C,MAAM,OAAO,GAAG,SAAS,0BAA0B,CAAC;IACpD,MAAM,SAAS,GAAG,SAAS,2CAA2C,CAAC;IAEvE,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;QAC/F,MAAM,IAAI,UACR,GAAG,OAAO,qCAAqC,EAAE,KAAK,2BAA2B,CAAC;IAEtF;AACF;AAEA,SAAS,0BAA0B,MAAM,EAAE,QAAQ;IACjD,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;QACrB,MAAM,IAAI,UAAU,GAAG,SAAS,iCAAiC,CAAC;IACpE;AACF;AAEA,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/pick.js"], "sourcesContent": ["module.exports = function pick(object, ...paths) {\n  const obj = {};\n  for (const path of paths) {\n    if (object[path] !== undefined) {\n      obj[path] = object[path];\n    }\n  }\n  return obj;\n};\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,SAAS,KAAK,MAAM,EAAE,GAAG,KAAK;IAC7C,MAAM,MAAM,CAAC;IACb,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,MAAM,CAAC,KAAK,KAAK,WAAW;YAC9B,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;QAC1B;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/process_response.js"], "sourcesContent": ["const { STATUS_CODES } = require('http');\nconst { format } = require('util');\n\nconst { OPError } = require('../errors');\nconst parseWwwAuthenticate = require('./www_authenticate_parser');\n\nconst throwAuthenticateErrors = (response) => {\n  const params = parseWwwAuthenticate(response.headers['www-authenticate']);\n\n  if (params.error) {\n    throw new OPError(params, response);\n  }\n};\n\nconst isStandardBodyError = (response) => {\n  let result = false;\n  try {\n    let jsonbody;\n    if (typeof response.body !== 'object' || Buffer.isBuffer(response.body)) {\n      jsonbody = JSON.parse(response.body);\n    } else {\n      jsonbody = response.body;\n    }\n    result = typeof jsonbody.error === 'string' && jsonbody.error.length;\n    if (result) Object.defineProperty(response, 'body', { value: jsonbody, configurable: true });\n  } catch (err) {}\n\n  return result;\n};\n\nfunction processResponse(response, { statusCode = 200, body = true, bearer = false } = {}) {\n  if (response.statusCode !== statusCode) {\n    if (bearer) {\n      throwAuthenticateErrors(response);\n    }\n\n    if (isStandardBodyError(response)) {\n      throw new OPError(response.body, response);\n    }\n\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s, got: %i %s',\n          statusCode,\n          STATUS_CODES[statusCode],\n          response.statusCode,\n          STATUS_CODES[response.statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  if (body && !response.body) {\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s with body but no body was returned',\n          statusCode,\n          STATUS_CODES[statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  return response.body;\n}\n\nmodule.exports = processResponse;\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,YAAY,EAAE;AACtB,MAAM,EAAE,MAAM,EAAE;AAEhB,MAAM,EAAE,OAAO,EAAE;AACjB,MAAM;AAEN,MAAM,0BAA0B,CAAC;IAC/B,MAAM,SAAS,qBAAqB,SAAS,OAAO,CAAC,mBAAmB;IAExE,IAAI,OAAO,KAAK,EAAE;QAChB,MAAM,IAAI,QAAQ,QAAQ;IAC5B;AACF;AAEA,MAAM,sBAAsB,CAAC;IAC3B,IAAI,SAAS;IACb,IAAI;QACF,IAAI;QACJ,IAAI,OAAO,SAAS,IAAI,KAAK,YAAY,OAAO,QAAQ,CAAC,SAAS,IAAI,GAAG;YACvE,WAAW,KAAK,KAAK,CAAC,SAAS,IAAI;QACrC,OAAO;YACL,WAAW,SAAS,IAAI;QAC1B;QACA,SAAS,OAAO,SAAS,KAAK,KAAK,YAAY,SAAS,KAAK,CAAC,MAAM;QACpE,IAAI,QAAQ,OAAO,cAAc,CAAC,UAAU,QAAQ;YAAE,OAAO;YAAU,cAAc;QAAK;IAC5F,EAAE,OAAO,KAAK,CAAC;IAEf,OAAO;AACT;AAEA,SAAS,gBAAgB,QAAQ,EAAE,EAAE,aAAa,GAAG,EAAE,OAAO,IAAI,EAAE,SAAS,KAAK,EAAE,GAAG,CAAC,CAAC;IACvF,IAAI,SAAS,UAAU,KAAK,YAAY;QACtC,IAAI,QAAQ;YACV,wBAAwB;QAC1B;QAEA,IAAI,oBAAoB,WAAW;YACjC,MAAM,IAAI,QAAQ,SAAS,IAAI,EAAE;QACnC;QAEA,MAAM,IAAI,QACR;YACE,OAAO,OACL,8BACA,YACA,YAAY,CAAC,WAAW,EACxB,SAAS,UAAU,EACnB,YAAY,CAAC,SAAS,UAAU,CAAC;QAErC,GACA;IAEJ;IAEA,IAAI,QAAQ,CAAC,SAAS,IAAI,EAAE;QAC1B,MAAM,IAAI,QACR;YACE,OAAO,OACL,qDACA,YACA,YAAY,CAAC,WAAW;QAE5B,GACA;IAEJ;IAEA,OAAO,SAAS,IAAI;AACtB;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/unix_timestamp.js"], "sourcesContent": ["module.exports = () => Math.floor(Date.now() / 1000);\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,IAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/token_set.js"], "sourcesContent": ["const base64url = require('./helpers/base64url');\nconst now = require('./helpers/unix_timestamp');\n\nclass TokenSet {\n  constructor(values) {\n    Object.assign(this, values);\n    const { constructor, ...properties } = Object.getOwnPropertyDescriptors(\n      this.constructor.prototype,\n    );\n\n    Object.defineProperties(this, properties);\n  }\n\n  set expires_in(value) {\n    this.expires_at = now() + Number(value);\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  claims() {\n    if (!this.id_token) {\n      throw new TypeError('id_token not present in TokenSet');\n    }\n\n    return JSON.parse(base64url.decode(this.id_token.split('.')[1]));\n  }\n}\n\nmodule.exports = TokenSet;\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AAEN,MAAM;IACJ,YAAY,MAAM,CAAE;QAClB,OAAO,MAAM,CAAC,IAAI,EAAE;QACpB,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,GAAG,OAAO,yBAAyB,CACrE,IAAI,CAAC,WAAW,CAAC,SAAS;QAG5B,OAAO,gBAAgB,CAAC,IAAI,EAAE;IAChC;IAEA,IAAI,WAAW,KAAK,EAAE;QACpB,IAAI,CAAC,UAAU,GAAG,QAAQ,OAAO;IACnC;IAEA,IAAI,aAAa;QACf,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM;YAAC,IAAI,CAAC,UAAU,GAAG;YAAO;SAAE;IAC1D;IAEA,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,KAAK;IAC7B;IAEA,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;IAChE;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/generators.js"], "sourcesContent": ["const { createHash, randomBytes } = require('crypto');\n\nconst base64url = require('./base64url');\n\nconst random = (bytes = 32) => base64url.encode(randomBytes(bytes));\n\nmodule.exports = {\n  random,\n  state: random,\n  nonce: random,\n  codeVerifier: random,\n  codeChallenge: (codeVerifier) =>\n    base64url.encode(createHash('sha256').update(codeVerifier).digest()),\n};\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE;AAEjC,MAAM;AAEN,MAAM,SAAS,CAAC,QAAQ,EAAE,GAAK,UAAU,MAAM,CAAC,YAAY;AAE5D,OAAO,OAAO,GAAG;IACf;IACA,OAAO;IACP,OAAO;IACP,cAAc;IACd,eAAe,CAAC,eACd,UAAU,MAAM,CAAC,WAAW,UAAU,MAAM,CAAC,cAAc,MAAM;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/consts.js"], "sourcesContent": ["const HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\n\nmodule.exports = {\n  CLOCK_TOLERANCE,\n  HTTP_OPTIONS,\n};\n"], "names": [], "mappings": "AAAA,MAAM,eAAe;AACrB,MAAM,kBAAkB;AAExB,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/request.js"], "sourcesContent": ["const assert = require('assert');\nconst querystring = require('querystring');\nconst http = require('http');\nconst https = require('https');\nconst { once } = require('events');\nconst { URL } = require('url');\n\nconst LRU = require('lru-cache');\n\nconst pkg = require('../../package.json');\nconst { RPError } = require('../errors');\n\nconst pick = require('./pick');\nconst { deep: defaultsDeep } = require('./defaults');\nconst { HTTP_OPTIONS } = require('./consts');\n\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\n\nconst allowed = [\n  'agent',\n  'ca',\n  'cert',\n  'crl',\n  'headers',\n  'key',\n  'lookup',\n  'passphrase',\n  'pfx',\n  'timeout',\n];\n\nconst setDefaults = (props, options) => {\n  DEFAULT_HTTP_OPTIONS = defaultsDeep(\n    {},\n    props.length ? pick(options, ...props) : options,\n    DEFAULT_HTTP_OPTIONS,\n  );\n};\n\nsetDefaults([], {\n  headers: {\n    'User-Agent': `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n    'Accept-Encoding': 'identity',\n  },\n  timeout: 3500,\n});\n\nfunction send(req, body, contentType) {\n  if (contentType) {\n    req.removeHeader('content-type');\n    req.setHeader('content-type', contentType);\n  }\n  if (body) {\n    req.removeHeader('content-length');\n    req.setHeader('content-length', Buffer.byteLength(body));\n    req.write(body);\n  }\n  req.end();\n}\n\nconst nonces = new LRU({ max: 100 });\n\nmodule.exports = async function request(options, { accessToken, mTLS = false, DPoP } = {}) {\n  let url;\n  try {\n    url = new URL(options.url);\n    delete options.url;\n    assert(/^(https?:)$/.test(url.protocol));\n  } catch (err) {\n    throw new TypeError('only valid absolute URLs can be requested');\n  }\n  const optsFn = this[HTTP_OPTIONS];\n  let opts = options;\n\n  const nonceKey = `${url.origin}${url.pathname}`;\n  if (DPoP && 'dpopProof' in this) {\n    opts.headers = opts.headers || {};\n    opts.headers.DPoP = await this.dpopProof(\n      {\n        htu: `${url.origin}${url.pathname}`,\n        htm: options.method || 'GET',\n        nonce: nonces.get(nonceKey),\n      },\n      DPoP,\n      accessToken,\n    );\n  }\n\n  let userOptions;\n  if (optsFn) {\n    userOptions = pick(\n      optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)),\n      ...allowed,\n    );\n  }\n  opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n\n  if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n    throw new TypeError('mutual-TLS certificate and key not set');\n  }\n\n  if (opts.searchParams) {\n    for (const [key, value] of Object.entries(opts.searchParams)) {\n      url.searchParams.delete(key);\n      url.searchParams.set(key, value);\n    }\n  }\n\n  let responseType;\n  let form;\n  let json;\n  let body;\n  ({ form, responseType, json, body, ...opts } = opts);\n\n  for (const [key, value] of Object.entries(opts.headers || {})) {\n    if (value === undefined) {\n      delete opts.headers[key];\n    }\n  }\n\n  let response;\n  const req = (url.protocol === 'https:' ? https.request : http.request)(url.href, opts);\n  return (async () => {\n    if (json) {\n      send(req, JSON.stringify(json), 'application/json');\n    } else if (form) {\n      send(req, querystring.stringify(form), 'application/x-www-form-urlencoded');\n    } else if (body) {\n      send(req, body);\n    } else {\n      send(req);\n    }\n\n    [response] = await Promise.race([once(req, 'response'), once(req, 'timeout')]);\n\n    // timeout reached\n    if (!response) {\n      req.destroy();\n      throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n    }\n\n    const parts = [];\n\n    for await (const part of response) {\n      parts.push(part);\n    }\n\n    if (parts.length) {\n      switch (responseType) {\n        case 'json': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              let value = Buffer.concat(parts);\n              try {\n                value = JSON.parse(value);\n              } catch (err) {\n                Object.defineProperty(err, 'response', { value: response });\n                throw err;\n              } finally {\n                Object.defineProperty(response, 'body', { value, configurable: true });\n              }\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        case undefined:\n        case 'buffer': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              const value = Buffer.concat(parts);\n              Object.defineProperty(response, 'body', { value, configurable: true });\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        default:\n          throw new TypeError('unsupported responseType request option');\n      }\n    }\n\n    return response;\n  })()\n    .catch((err) => {\n      if (response) Object.defineProperty(err, 'response', { value: response });\n      throw err;\n    })\n    .finally(() => {\n      const dpopNonce = response && response.headers['dpop-nonce'];\n      if (dpopNonce && NQCHAR.test(dpopNonce)) {\n        nonces.set(nonceKey, dpopNonce);\n      }\n    });\n};\n\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,IAAI,EAAE;AACd,MAAM,EAAE,GAAG,EAAE;AAEb,MAAM;AAEN,MAAM;AACN,MAAM,EAAE,OAAO,EAAE;AAEjB,MAAM;AACN,MAAM,EAAE,MAAM,YAAY,EAAE;AAC5B,MAAM,EAAE,YAAY,EAAE;AAEtB,IAAI;AACJ,MAAM,SAAS;AAEf,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,cAAc,CAAC,OAAO;IAC1B,uBAAuB,aACrB,CAAC,GACD,MAAM,MAAM,GAAG,KAAK,YAAY,SAAS,SACzC;AAEJ;AAEA,YAAY,EAAE,EAAE;IACd,SAAS;QACP,cAAc,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,EAAE,IAAI,QAAQ,CAAC,CAAC,CAAC;QAC5D,mBAAmB;IACrB;IACA,SAAS;AACX;AAEA,SAAS,KAAK,GAAG,EAAE,IAAI,EAAE,WAAW;IAClC,IAAI,aAAa;QACf,IAAI,YAAY,CAAC;QACjB,IAAI,SAAS,CAAC,gBAAgB;IAChC;IACA,IAAI,MAAM;QACR,IAAI,YAAY,CAAC;QACjB,IAAI,SAAS,CAAC,kBAAkB,OAAO,UAAU,CAAC;QAClD,IAAI,KAAK,CAAC;IACZ;IACA,IAAI,GAAG;AACT;AAEA,MAAM,SAAS,IAAI,IAAI;IAAE,KAAK;AAAI;AAElC,OAAO,OAAO,GAAG,eAAe,QAAQ,OAAO,EAAE,EAAE,WAAW,EAAE,OAAO,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACvF,IAAI;IACJ,IAAI;QACF,MAAM,IAAI,IAAI,QAAQ,GAAG;QACzB,OAAO,QAAQ,GAAG;QAClB,OAAO,cAAc,IAAI,CAAC,IAAI,QAAQ;IACxC,EAAE,OAAO,KAAK;QACZ,MAAM,IAAI,UAAU;IACtB;IACA,MAAM,SAAS,IAAI,CAAC,aAAa;IACjC,IAAI,OAAO;IAEX,MAAM,WAAW,GAAG,IAAI,MAAM,GAAG,IAAI,QAAQ,EAAE;IAC/C,IAAI,QAAQ,eAAe,IAAI,EAAE;QAC/B,KAAK,OAAO,GAAG,KAAK,OAAO,IAAI,CAAC;QAChC,KAAK,OAAO,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CACtC;YACE,KAAK,GAAG,IAAI,MAAM,GAAG,IAAI,QAAQ,EAAE;YACnC,KAAK,QAAQ,MAAM,IAAI;YACvB,OAAO,OAAO,GAAG,CAAC;QACpB,GACA,MACA;IAEJ;IAEA,IAAI;IACJ,IAAI,QAAQ;QACV,cAAc,KACZ,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,aAAa,CAAC,GAAG,MAAM,2BAC3C;IAEP;IACA,OAAO,aAAa,CAAC,GAAG,aAAa,MAAM;IAE3C,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,IAAI,GAAG;QACjD,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,KAAK,YAAY,EAAE;QACrB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAK,YAAY,EAAG;YAC5D,IAAI,YAAY,CAAC,MAAM,CAAC;YACxB,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK;QAC5B;IACF;IAEA,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,GAAG,IAAI;IAEnD,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAK,OAAO,IAAI,CAAC,GAAI;QAC7D,IAAI,UAAU,WAAW;YACvB,OAAO,KAAK,OAAO,CAAC,IAAI;QAC1B;IACF;IAEA,IAAI;IACJ,MAAM,MAAM,CAAC,IAAI,QAAQ,KAAK,WAAW,MAAM,OAAO,GAAG,KAAK,OAAO,EAAE,IAAI,IAAI,EAAE;IACjF,OAAO,CAAC;QACN,IAAI,MAAM;YACR,KAAK,KAAK,KAAK,SAAS,CAAC,OAAO;QAClC,OAAO,IAAI,MAAM;YACf,KAAK,KAAK,YAAY,SAAS,CAAC,OAAO;QACzC,OAAO,IAAI,MAAM;YACf,KAAK,KAAK;QACZ,OAAO;YACL,KAAK;QACP;QAEA,CAAC,SAAS,GAAG,MAAM,QAAQ,IAAI,CAAC;YAAC,KAAK,KAAK;YAAa,KAAK,KAAK;SAAW;QAE7E,kBAAkB;QAClB,IAAI,CAAC,UAAU;YACb,IAAI,OAAO;YACX,MAAM,IAAI,QAAQ,CAAC,iCAAiC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC;QACxE;QAEA,MAAM,QAAQ,EAAE;QAEhB,WAAW,MAAM,QAAQ,SAAU;YACjC,MAAM,IAAI,CAAC;QACb;QAEA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAQ;gBACN,KAAK;oBAAQ;wBACX,OAAO,cAAc,CAAC,UAAU,QAAQ;4BACtC;gCACE,IAAI,QAAQ,OAAO,MAAM,CAAC;gCAC1B,IAAI;oCACF,QAAQ,KAAK,KAAK,CAAC;gCACrB,EAAE,OAAO,KAAK;oCACZ,OAAO,cAAc,CAAC,KAAK,YAAY;wCAAE,OAAO;oCAAS;oCACzD,MAAM;gCACR,SAAU;oCACR,OAAO,cAAc,CAAC,UAAU,QAAQ;wCAAE;wCAAO,cAAc;oCAAK;gCACtE;gCACA,OAAO;4BACT;4BACA,cAAc;wBAChB;wBACA;oBACF;gBACA,KAAK;gBACL,KAAK;oBAAU;wBACb,OAAO,cAAc,CAAC,UAAU,QAAQ;4BACtC;gCACE,MAAM,QAAQ,OAAO,MAAM,CAAC;gCAC5B,OAAO,cAAc,CAAC,UAAU,QAAQ;oCAAE;oCAAO,cAAc;gCAAK;gCACpE,OAAO;4BACT;4BACA,cAAc;wBAChB;wBACA;oBACF;gBACA;oBACE,MAAM,IAAI,UAAU;YACxB;QACF;QAEA,OAAO;IACT,CAAC,IACE,KAAK,CAAC,CAAC;QACN,IAAI,UAAU,OAAO,cAAc,CAAC,KAAK,YAAY;YAAE,OAAO;QAAS;QACvE,MAAM;IACR,GACC,OAAO,CAAC;QACP,MAAM,YAAY,YAAY,SAAS,OAAO,CAAC,aAAa;QAC5D,IAAI,aAAa,OAAO,IAAI,CAAC,YAAY;YACvC,OAAO,GAAG,CAAC,UAAU;QACvB;IACF;AACJ;AAEA,OAAO,OAAO,CAAC,WAAW,GAAG,YAAY,IAAI,CAAC,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/weak_cache.js"], "sourcesContent": ["module.exports.keystores = new WeakMap();\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,CAAC,SAAS,GAAG,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/deep_clone.js"], "sourcesContent": ["module.exports = globalThis.structuredClone || ((obj) => JSON.parse(JSON.stringify(obj)));\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,WAAW,eAAe,IAAI,CAAC,CAAC,MAAQ,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/keystore.js"], "sourcesContent": ["const jose = require('jose');\n\nconst clone = require('./deep_clone');\nconst isPlainObject = require('./is_plain_object');\n\nconst internal = Symbol();\n\nconst keyscore = (key, { alg, use }) => {\n  let score = 0;\n\n  if (alg && key.alg) {\n    score++;\n  }\n\n  if (use && key.use) {\n    score++;\n  }\n\n  return score;\n};\n\nfunction getKtyFromAlg(alg) {\n  switch (typeof alg === 'string' && alg.slice(0, 2)) {\n    case 'RS':\n    case 'PS':\n      return 'RSA';\n    case 'ES':\n      return 'EC';\n    case 'Ed':\n      return 'OKP';\n    default:\n      return undefined;\n  }\n}\n\nfunction getAlgorithms(use, alg, kty, crv) {\n  // Ed25519, Ed448, and secp256k1 always have \"alg\"\n  // OKP always has \"use\"\n  if (alg) {\n    return new Set([alg]);\n  }\n\n  switch (kty) {\n    case 'EC': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n      }\n\n      if (use === 'sig' || use === undefined) {\n        switch (crv) {\n          case 'P-256':\n          case 'P-384':\n            algs = algs.concat([`ES${crv.slice(-3)}`]);\n            break;\n          case 'P-521':\n            algs = algs.concat(['ES512']);\n            break;\n          case 'secp256k1':\n            if (jose.cryptoRuntime === 'node:crypto') {\n              algs = algs.concat(['ES256K']);\n            }\n            break;\n        }\n      }\n\n      return new Set(algs);\n    }\n    case 'OKP': {\n      return new Set(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n    }\n    case 'RSA': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['RSA-OAEP', 'RSA-OAEP-256', 'RSA-OAEP-384', 'RSA-OAEP-512']);\n        if (jose.cryptoRuntime === 'node:crypto') {\n          algs = algs.concat(['RSA1_5']);\n        }\n      }\n\n      if (use === 'sig' || use === undefined) {\n        algs = algs.concat(['PS256', 'PS384', 'PS512', 'RS256', 'RS384', 'RS512']);\n      }\n\n      return new Set(algs);\n    }\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nmodule.exports = class KeyStore {\n  #keys;\n\n  constructor(i, keys) {\n    if (i !== internal) throw new Error('invalid constructor call');\n    this.#keys = keys;\n  }\n\n  toJWKS() {\n    return {\n      keys: this.map(({ jwk: { d, p, q, dp, dq, qi, ...jwk } }) => jwk),\n    };\n  }\n\n  all({ alg, kid, use } = {}) {\n    if (!use || !alg) {\n      throw new Error();\n    }\n\n    const kty = getKtyFromAlg(alg);\n\n    const search = { alg, use };\n    return this.filter((key) => {\n      let candidate = true;\n\n      if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n        candidate = false;\n      }\n\n      if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n        candidate = false;\n      }\n\n      if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n        candidate = false;\n      }\n\n      if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n        candidate = false;\n      } else if (!key.algorithms.has(alg)) {\n        candidate = false;\n      }\n\n      return candidate;\n    }).sort((first, second) => keyscore(second, search) - keyscore(first, search));\n  }\n\n  get(...args) {\n    return this.all(...args)[0];\n  }\n\n  static async fromJWKS(jwks, { onlyPublic = false, onlyPrivate = false } = {}) {\n    if (\n      !isPlainObject(jwks) ||\n      !Array.isArray(jwks.keys) ||\n      jwks.keys.some((k) => !isPlainObject(k) || !('kty' in k))\n    ) {\n      throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n\n    const keys = [];\n\n    for (let jwk of jwks.keys) {\n      jwk = clone(jwk);\n      const { kty, kid, crv } = jwk;\n\n      let { alg, use } = jwk;\n\n      if (typeof kty !== 'string' || !kty) {\n        continue;\n      }\n\n      if (use !== undefined && use !== 'sig' && use !== 'enc') {\n        continue;\n      }\n\n      if (typeof alg !== 'string' && alg !== undefined) {\n        continue;\n      }\n\n      if (typeof kid !== 'string' && kid !== undefined) {\n        continue;\n      }\n\n      if (kty === 'EC' && use === 'sig') {\n        switch (crv) {\n          case 'P-256':\n            alg = 'ES256';\n            break;\n          case 'P-384':\n            alg = 'ES384';\n            break;\n          case 'P-521':\n            alg = 'ES512';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (crv === 'secp256k1') {\n        use = 'sig';\n        alg = 'ES256K';\n      }\n\n      if (kty === 'OKP') {\n        switch (crv) {\n          case 'Ed25519':\n          case 'Ed448':\n            use = 'sig';\n            alg = 'EdDSA';\n            break;\n          case 'X25519':\n          case 'X448':\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (alg && !use) {\n        switch (true) {\n          case alg.startsWith('ECDH'):\n            use = 'enc';\n            break;\n          case alg.startsWith('RSA'):\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (onlyPrivate && (jwk.kty === 'oct' || !jwk.d)) {\n        throw new Error('jwks must only contain private keys');\n      }\n\n      if (onlyPublic && (jwk.d || jwk.k)) {\n        continue;\n      }\n\n      keys.push({\n        jwk: { ...jwk, alg, use },\n        async keyObject(alg) {\n          if (this[alg]) {\n            return this[alg];\n          }\n\n          const keyObject = await jose.importJWK(this.jwk, alg);\n          this[alg] = keyObject;\n          return keyObject;\n        },\n        get algorithms() {\n          Object.defineProperty(this, 'algorithms', {\n            value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n            enumerable: true,\n            configurable: false,\n          });\n          return this.algorithms;\n        },\n      });\n    }\n\n    return new this(internal, keys);\n  }\n\n  filter(...args) {\n    return this.#keys.filter(...args);\n  }\n\n  find(...args) {\n    return this.#keys.find(...args);\n  }\n\n  every(...args) {\n    return this.#keys.every(...args);\n  }\n\n  some(...args) {\n    return this.#keys.some(...args);\n  }\n\n  map(...args) {\n    return this.#keys.map(...args);\n  }\n\n  forEach(...args) {\n    return this.#keys.forEach(...args);\n  }\n\n  reduce(...args) {\n    return this.#keys.reduce(...args);\n  }\n\n  sort(...args) {\n    return this.#keys.sort(...args);\n  }\n\n  *[Symbol.iterator]() {\n    for (const key of this.#keys) {\n      yield key;\n    }\n  }\n};\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,MAAM;AACN,MAAM;AAEN,MAAM,WAAW;AAEjB,MAAM,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;IACjC,IAAI,QAAQ;IAEZ,IAAI,OAAO,IAAI,GAAG,EAAE;QAClB;IACF;IAEA,IAAI,OAAO,IAAI,GAAG,EAAE;QAClB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,cAAc,GAAG;IACxB,OAAQ,OAAO,QAAQ,YAAY,IAAI,KAAK,CAAC,GAAG;QAC9C,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,SAAS,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACvC,kDAAkD;IAClD,uBAAuB;IACvB,IAAI,KAAK;QACP,OAAO,IAAI,IAAI;YAAC;SAAI;IACtB;IAEA,OAAQ;QACN,KAAK;YAAM;gBACT,IAAI,OAAO,EAAE;gBAEb,IAAI,QAAQ,SAAS,QAAQ,WAAW;oBACtC,OAAO,KAAK,MAAM,CAAC;wBAAC;wBAAW;wBAAkB;wBAAkB;qBAAiB;gBACtF;gBAEA,IAAI,QAAQ,SAAS,QAAQ,WAAW;oBACtC,OAAQ;wBACN,KAAK;wBACL,KAAK;4BACH,OAAO,KAAK,MAAM,CAAC;gCAAC,CAAC,EAAE,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI;6BAAC;4BACzC;wBACF,KAAK;4BACH,OAAO,KAAK,MAAM,CAAC;gCAAC;6BAAQ;4BAC5B;wBACF,KAAK;4BACH,IAAI,KAAK,aAAa,KAAK,eAAe;gCACxC,OAAO,KAAK,MAAM,CAAC;oCAAC;iCAAS;4BAC/B;4BACA;oBACJ;gBACF;gBAEA,OAAO,IAAI,IAAI;YACjB;QACA,KAAK;YAAO;gBACV,OAAO,IAAI,IAAI;oBAAC;oBAAW;oBAAkB;oBAAkB;iBAAiB;YAClF;QACA,KAAK;YAAO;gBACV,IAAI,OAAO,EAAE;gBAEb,IAAI,QAAQ,SAAS,QAAQ,WAAW;oBACtC,OAAO,KAAK,MAAM,CAAC;wBAAC;wBAAY;wBAAgB;wBAAgB;qBAAe;oBAC/E,IAAI,KAAK,aAAa,KAAK,eAAe;wBACxC,OAAO,KAAK,MAAM,CAAC;4BAAC;yBAAS;oBAC/B;gBACF;gBAEA,IAAI,QAAQ,SAAS,QAAQ,WAAW;oBACtC,OAAO,KAAK,MAAM,CAAC;wBAAC;wBAAS;wBAAS;wBAAS;wBAAS;wBAAS;qBAAQ;gBAC3E;gBAEA,OAAO,IAAI,IAAI;YACjB;QACA;YACE,MAAM,IAAI,MAAM;IACpB;AACF;AAEA,OAAO,OAAO,GAAG,MAAM;IACrB,CAAA,IAAK,CAAC;IAEN,YAAY,CAAC,EAAE,IAAI,CAAE;QACnB,IAAI,MAAM,UAAU,MAAM,IAAI,MAAM;QACpC,IAAI,CAAC,CAAA,IAAK,GAAG;IACf;IAEA,SAAS;QACP,OAAO;YACL,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,GAAK;QAC/D;IACF;IAEA,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,KAAK;YAChB,MAAM,IAAI;QACZ;QAEA,MAAM,MAAM,cAAc;QAE1B,MAAM,SAAS;YAAE;YAAK;QAAI;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;YAClB,IAAI,YAAY;YAEhB,IAAI,aAAa,QAAQ,aAAa,IAAI,GAAG,CAAC,GAAG,KAAK,KAAK;gBACzD,YAAY;YACd;YAEA,IAAI,aAAa,QAAQ,aAAa,IAAI,GAAG,CAAC,GAAG,KAAK,KAAK;gBACzD,YAAY;YACd;YAEA,IAAI,aAAa,QAAQ,aAAa,IAAI,GAAG,CAAC,GAAG,KAAK,aAAa,IAAI,GAAG,CAAC,GAAG,KAAK,KAAK;gBACtF,YAAY;YACd;YAEA,IAAI,aAAa,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,KAAK;gBACnD,YAAY;YACd,OAAO,IAAI,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM;gBACnC,YAAY;YACd;YAEA,OAAO;QACT,GAAG,IAAI,CAAC,CAAC,OAAO,SAAW,SAAS,QAAQ,UAAU,SAAS,OAAO;IACxE;IAEA,IAAI,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;IAC7B;IAEA,aAAa,SAAS,IAAI,EAAE,EAAE,aAAa,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QAC5E,IACE,CAAC,cAAc,SACf,CAAC,MAAM,OAAO,CAAC,KAAK,IAAI,KACxB,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAM,CAAC,cAAc,MAAM,CAAC,CAAC,SAAS,CAAC,IACvD;YACA,MAAM,IAAI,UAAU;QACtB;QAEA,MAAM,OAAO,EAAE;QAEf,KAAK,IAAI,OAAO,KAAK,IAAI,CAAE;YACzB,MAAM,MAAM;YACZ,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;YAE1B,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;YAEnB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;gBACnC;YACF;YAEA,IAAI,QAAQ,aAAa,QAAQ,SAAS,QAAQ,OAAO;gBACvD;YACF;YAEA,IAAI,OAAO,QAAQ,YAAY,QAAQ,WAAW;gBAChD;YACF;YAEA,IAAI,OAAO,QAAQ,YAAY,QAAQ,WAAW;gBAChD;YACF;YAEA,IAAI,QAAQ,QAAQ,QAAQ,OAAO;gBACjC,OAAQ;oBACN,KAAK;wBACH,MAAM;wBACN;oBACF,KAAK;wBACH,MAAM;wBACN;oBACF,KAAK;wBACH,MAAM;wBACN;oBACF;wBACE;gBACJ;YACF;YAEA,IAAI,QAAQ,aAAa;gBACvB,MAAM;gBACN,MAAM;YACR;YAEA,IAAI,QAAQ,OAAO;gBACjB,OAAQ;oBACN,KAAK;oBACL,KAAK;wBACH,MAAM;wBACN,MAAM;wBACN;oBACF,KAAK;oBACL,KAAK;wBACH,MAAM;wBACN;oBACF;wBACE;gBACJ;YACF;YAEA,IAAI,OAAO,CAAC,KAAK;gBACf,OAAQ;oBACN,KAAK,IAAI,UAAU,CAAC;wBAClB,MAAM;wBACN;oBACF,KAAK,IAAI,UAAU,CAAC;wBAClB,MAAM;wBACN;oBACF;wBACE;gBACJ;YACF;YAEA,IAAI,eAAe,CAAC,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI,CAAC,GAAG;gBAChD,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG;gBAClC;YACF;YAEA,KAAK,IAAI,CAAC;gBACR,KAAK;oBAAE,GAAG,GAAG;oBAAE;oBAAK;gBAAI;gBACxB,MAAM,WAAU,GAAG;oBACjB,IAAI,IAAI,CAAC,IAAI,EAAE;wBACb,OAAO,IAAI,CAAC,IAAI;oBAClB;oBAEA,MAAM,YAAY,MAAM,KAAK,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE;oBACjD,IAAI,CAAC,IAAI,GAAG;oBACZ,OAAO;gBACT;gBACA,IAAI,cAAa;oBACf,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;wBACxC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG;wBAC3E,YAAY;wBACZ,cAAc;oBAChB;oBACA,OAAO,IAAI,CAAC,UAAU;gBACxB;YACF;QACF;QAEA,OAAO,IAAI,IAAI,CAAC,UAAU;IAC5B;IAEA,OAAO,GAAG,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,MAAM,IAAI;IAC9B;IAEA,KAAK,GAAG,IAAI,EAAE;QACZ,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,IAAI,IAAI;IAC5B;IAEA,MAAM,GAAG,IAAI,EAAE;QACb,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,KAAK,IAAI;IAC7B;IAEA,KAAK,GAAG,IAAI,EAAE;QACZ,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,IAAI,IAAI;IAC5B;IAEA,IAAI,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,GAAG,IAAI;IAC3B;IAEA,QAAQ,GAAG,IAAI,EAAE;QACf,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,OAAO,IAAI;IAC/B;IAEA,OAAO,GAAG,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,MAAM,IAAI;IAC9B;IAEA,KAAK,GAAG,IAAI,EAAE;QACZ,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,IAAI,IAAI;IAC5B;IAEA,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QACnB,KAAK,MAAM,OAAO,IAAI,CAAC,CAAA,IAAK,CAAE;YAC5B,MAAM;QACR;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/merge.js"], "sourcesContent": ["const isPlainObject = require('./is_plain_object');\n\nfunction merge(target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (isPlainObject(target[key]) && isPlainObject(value)) {\n        target[key] = merge(target[key], value);\n      } else if (typeof value !== 'undefined') {\n        target[key] = value;\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = merge;\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,SAAS,MAAM,MAAM,EAAE,GAAG,OAAO;IAC/B,KAAK,MAAM,UAAU,QAAS;QAC5B,IAAI,CAAC,cAAc,SAAS;YAC1B;QACF;QACA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;YACjD,sBAAsB,GACtB,IAAI,QAAQ,eAAe,QAAQ,eAAe;gBAChD;YACF;YACA,IAAI,cAAc,MAAM,CAAC,IAAI,KAAK,cAAc,QAAQ;gBACtD,MAAM,CAAC,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE;YACnC,OAAO,IAAI,OAAO,UAAU,aAAa;gBACvC,MAAM,CAAC,IAAI,GAAG;YAChB;QACF;IACF;IAEA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/client.js"], "sourcesContent": ["const jose = require('jose');\n\nconst { RPError } = require('../errors');\n\nconst { assertIssuerConfiguration } = require('./assert');\nconst { random } = require('./generators');\nconst now = require('./unix_timestamp');\nconst request = require('./request');\nconst { keystores } = require('./weak_cache');\nconst merge = require('./merge');\n\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = (value) => encodeURIComponent(value).replace(/%20/g, '+');\n\nasync function clientAssertion(endpoint, payload) {\n  let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n  if (!alg) {\n    assertIssuerConfiguration(\n      this.issuer,\n      `${endpoint}_endpoint_auth_signing_alg_values_supported`,\n    );\n  }\n\n  if (this[`${endpoint}_endpoint_auth_method`] === 'client_secret_jwt') {\n    if (!alg) {\n      const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n      alg =\n        Array.isArray(supported) && supported.find((signAlg) => /^HS(?:256|384|512)/.test(signAlg));\n    }\n\n    if (!alg) {\n      throw new RPError(\n        `failed to determine a JWS Algorithm to use for ${\n          this[`${endpoint}_endpoint_auth_method`]\n        } Client Assertion`,\n      );\n    }\n\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n      .setProtectedHeader({ alg })\n      .sign(this.secretForAlg(alg));\n  }\n\n  const keystore = await keystores.get(this);\n\n  if (!keystore) {\n    throw new TypeError('no client jwks provided for signing a client assertion with');\n  }\n\n  if (!alg) {\n    const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n    alg =\n      Array.isArray(supported) &&\n      supported.find((signAlg) => keystore.get({ alg: signAlg, use: 'sig' }));\n  }\n\n  if (!alg) {\n    throw new RPError(\n      `failed to determine a JWS Algorithm to use for ${\n        this[`${endpoint}_endpoint_auth_method`]\n      } Client Assertion`,\n    );\n  }\n\n  const key = keystore.get({ alg, use: 'sig' });\n  if (!key) {\n    throw new RPError(\n      `no key found in client jwks to sign a client assertion with using alg ${alg}`,\n    );\n  }\n\n  return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n    .setProtectedHeader({ alg, kid: key.jwk && key.jwk.kid })\n    .sign(await key.keyObject(alg));\n}\n\nasync function authFor(endpoint, { clientAssertionPayload } = {}) {\n  const authMethod = this[`${endpoint}_endpoint_auth_method`];\n  switch (authMethod) {\n    case 'self_signed_tls_client_auth':\n    case 'tls_client_auth':\n    case 'none':\n      return { form: { client_id: this.client_id } };\n    case 'client_secret_post':\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_post client authentication method requires a client_secret',\n        );\n      }\n      return { form: { client_id: this.client_id, client_secret: this.client_secret } };\n    case 'private_key_jwt':\n    case 'client_secret_jwt': {\n      const timestamp = now();\n\n      const assertion = await clientAssertion.call(this, endpoint, {\n        iat: timestamp,\n        exp: timestamp + 60,\n        jti: random(),\n        iss: this.client_id,\n        sub: this.client_id,\n        aud: this.issuer.issuer,\n        ...clientAssertionPayload,\n      });\n\n      return {\n        form: {\n          client_id: this.client_id,\n          client_assertion: assertion,\n          client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',\n        },\n      };\n    }\n    case 'client_secret_basic': {\n      // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n      // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n      // > The client identifier is encoded using the\n      // > \"application/x-www-form-urlencoded\" encoding algorithm per\n      // > Appendix B, and the encoded value is used as the username; the client\n      // > password is encoded using the same algorithm and used as the\n      // > password.\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_basic client authentication method requires a client_secret',\n        );\n      }\n      const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n      const value = Buffer.from(encoded).toString('base64');\n      return { headers: { Authorization: `Basic ${value}` } };\n    }\n    default: {\n      throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n    }\n  }\n}\n\nfunction resolveResponseType() {\n  const { length, 0: value } = this.response_types;\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nfunction resolveRedirectUri() {\n  const { length, 0: value } = this.redirect_uris || [];\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nasync function authenticatedPost(\n  endpoint,\n  opts,\n  { clientAssertionPayload, endpointAuthMethod = endpoint, DPoP } = {},\n) {\n  const auth = await authFor.call(this, endpointAuthMethod, { clientAssertionPayload });\n  const requestOpts = merge(opts, auth);\n\n  const mTLS =\n    this[`${endpointAuthMethod}_endpoint_auth_method`].includes('tls_client_auth') ||\n    (endpoint === 'token' && this.tls_client_certificate_bound_access_tokens);\n\n  let targetUrl;\n  if (mTLS && this.issuer.mtls_endpoint_aliases) {\n    targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n  }\n\n  targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n\n  if ('form' in requestOpts) {\n    for (const [key, value] of Object.entries(requestOpts.form)) {\n      if (typeof value === 'undefined') {\n        delete requestOpts.form[key];\n      }\n    }\n  }\n\n  return request.call(\n    this,\n    {\n      ...requestOpts,\n      method: 'POST',\n      url: targetUrl,\n      headers: {\n        ...(endpoint !== 'revocation'\n          ? {\n              Accept: 'application/json',\n            }\n          : undefined),\n        ...requestOpts.headers,\n      },\n    },\n    { mTLS, DPoP },\n  );\n}\n\nmodule.exports = {\n  resolveResponseType,\n  resolveRedirectUri,\n  authFor,\n  authenticatedPost,\n};\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,MAAM,EAAE,OAAO,EAAE;AAEjB,MAAM,EAAE,yBAAyB,EAAE;AACnC,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM;AACN,MAAM;AACN,MAAM,EAAE,SAAS,EAAE;AACnB,MAAM;AAEN,uEAAuE;AACvE,8FAA8F;AAC9F,MAAM,gBAAgB,CAAC,QAAU,mBAAmB,OAAO,OAAO,CAAC,QAAQ;AAE3E,eAAe,gBAAgB,QAAQ,EAAE,OAAO;IAC9C,IAAI,MAAM,IAAI,CAAC,GAAG,SAAS,0BAA0B,CAAC,CAAC;IACvD,IAAI,CAAC,KAAK;QACR,0BACE,IAAI,CAAC,MAAM,EACX,GAAG,SAAS,2CAA2C,CAAC;IAE5D;IAEA,IAAI,IAAI,CAAC,GAAG,SAAS,qBAAqB,CAAC,CAAC,KAAK,qBAAqB;QACpE,IAAI,CAAC,KAAK;YACR,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,2CAA2C,CAAC,CAAC;YACvF,MACE,MAAM,OAAO,CAAC,cAAc,UAAU,IAAI,CAAC,CAAC,UAAY,qBAAqB,IAAI,CAAC;QACtF;QAEA,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,QACR,CAAC,+CAA+C,EAC9C,IAAI,CAAC,GAAG,SAAS,qBAAqB,CAAC,CAAC,CACzC,iBAAiB,CAAC;QAEvB;QAEA,OAAO,IAAI,KAAK,WAAW,CAAC,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,WACpD,kBAAkB,CAAC;YAAE;QAAI,GACzB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;IAC5B;IAEA,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,IAAI;IAEzC,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,CAAC,KAAK;QACR,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,2CAA2C,CAAC,CAAC;QACvF,MACE,MAAM,OAAO,CAAC,cACd,UAAU,IAAI,CAAC,CAAC,UAAY,SAAS,GAAG,CAAC;gBAAE,KAAK;gBAAS,KAAK;YAAM;IACxE;IAEA,IAAI,CAAC,KAAK;QACR,MAAM,IAAI,QACR,CAAC,+CAA+C,EAC9C,IAAI,CAAC,GAAG,SAAS,qBAAqB,CAAC,CAAC,CACzC,iBAAiB,CAAC;IAEvB;IAEA,MAAM,MAAM,SAAS,GAAG,CAAC;QAAE;QAAK,KAAK;IAAM;IAC3C,IAAI,CAAC,KAAK;QACR,MAAM,IAAI,QACR,CAAC,sEAAsE,EAAE,KAAK;IAElF;IAEA,OAAO,IAAI,KAAK,WAAW,CAAC,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,WACpD,kBAAkB,CAAC;QAAE;QAAK,KAAK,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG;IAAC,GACtD,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;AAC9B;AAEA,eAAe,QAAQ,QAAQ,EAAE,EAAE,sBAAsB,EAAE,GAAG,CAAC,CAAC;IAC9D,MAAM,aAAa,IAAI,CAAC,GAAG,SAAS,qBAAqB,CAAC,CAAC;IAC3D,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;gBAAE,MAAM;oBAAE,WAAW,IAAI,CAAC,SAAS;gBAAC;YAAE;QAC/C,KAAK;YACH,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,UAAU;gBAC1C,MAAM,IAAI,UACR;YAEJ;YACA,OAAO;gBAAE,MAAM;oBAAE,WAAW,IAAI,CAAC,SAAS;oBAAE,eAAe,IAAI,CAAC,aAAa;gBAAC;YAAE;QAClF,KAAK;QACL,KAAK;YAAqB;gBACxB,MAAM,YAAY;gBAElB,MAAM,YAAY,MAAM,gBAAgB,IAAI,CAAC,IAAI,EAAE,UAAU;oBAC3D,KAAK;oBACL,KAAK,YAAY;oBACjB,KAAK;oBACL,KAAK,IAAI,CAAC,SAAS;oBACnB,KAAK,IAAI,CAAC,SAAS;oBACnB,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM;oBACvB,GAAG,sBAAsB;gBAC3B;gBAEA,OAAO;oBACL,MAAM;wBACJ,WAAW,IAAI,CAAC,SAAS;wBACzB,kBAAkB;wBAClB,uBAAuB;oBACzB;gBACF;YACF;QACA,KAAK;YAAuB;gBAC1B,2FAA2F;gBAC3F,+EAA+E;gBAC/E,+CAA+C;gBAC/C,+DAA+D;gBAC/D,0EAA0E;gBAC1E,iEAAiE;gBACjE,cAAc;gBACd,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,UAAU;oBAC1C,MAAM,IAAI,UACR;gBAEJ;gBACA,MAAM,UAAU,GAAG,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,cAAc,IAAI,CAAC,aAAa,GAAG;gBACvF,MAAM,QAAQ,OAAO,IAAI,CAAC,SAAS,QAAQ,CAAC;gBAC5C,OAAO;oBAAE,SAAS;wBAAE,eAAe,CAAC,MAAM,EAAE,OAAO;oBAAC;gBAAE;YACxD;QACA;YAAS;gBACP,MAAM,IAAI,UAAU,CAAC,yBAAyB,EAAE,SAAS,qBAAqB,CAAC;YACjF;IACF;AACF;AAEA,SAAS;IACP,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC,cAAc;IAEhD,IAAI,WAAW,GAAG;QAChB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS;IACP,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE;IAErD,IAAI,WAAW,GAAG;QAChB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,eAAe,kBACb,QAAQ,EACR,IAAI,EACJ,EAAE,sBAAsB,EAAE,qBAAqB,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IAEpE,MAAM,OAAO,MAAM,QAAQ,IAAI,CAAC,IAAI,EAAE,oBAAoB;QAAE;IAAuB;IACnF,MAAM,cAAc,MAAM,MAAM;IAEhC,MAAM,OACJ,IAAI,CAAC,GAAG,mBAAmB,qBAAqB,CAAC,CAAC,CAAC,QAAQ,CAAC,sBAC3D,aAAa,WAAW,IAAI,CAAC,0CAA0C;IAE1E,IAAI;IACJ,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;QAC7C,YAAY,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC;IACvE;IAEA,YAAY,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC;IAE5D,IAAI,UAAU,aAAa;QACzB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,YAAY,IAAI,EAAG;YAC3D,IAAI,OAAO,UAAU,aAAa;gBAChC,OAAO,YAAY,IAAI,CAAC,IAAI;YAC9B;QACF;IACF;IAEA,OAAO,QAAQ,IAAI,CACjB,IAAI,EACJ;QACE,GAAG,WAAW;QACd,QAAQ;QACR,KAAK;QACL,SAAS;YACP,GAAI,aAAa,eACb;gBACE,QAAQ;YACV,IACA,SAAS;YACb,GAAG,YAAY,OAAO;QACxB;IACF,GACA;QAAE;QAAM;IAAK;AAEjB;AAEA,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/issuer.js"], "sourcesContent": ["const objectHash = require('object-hash');\nconst LRU = require('lru-cache');\n\nconst { RPError } = require('../errors');\n\nconst { assertIssuerConfiguration } = require('./assert');\nconst KeyStore = require('./keystore');\nconst { keystores } = require('./weak_cache');\nconst processResponse = require('./process_response');\nconst request = require('./request');\n\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = (ctx) => {\n  if (!caches.has(ctx)) {\n    caches.set(ctx, new LRU({ max: 100 }));\n  }\n  return caches.get(ctx);\n};\n\nasync function getKeyStore(reload = false) {\n  assertIssuerConfiguration(this, 'jwks_uri');\n\n  const keystore = keystores.get(this);\n  const cache = lrus(this);\n\n  if (reload || !keystore) {\n    if (inFlight.has(this)) {\n      return inFlight.get(this);\n    }\n    cache.reset();\n    inFlight.set(\n      this,\n      (async () => {\n        const response = await request\n          .call(this, {\n            method: 'GET',\n            responseType: 'json',\n            url: this.jwks_uri,\n            headers: {\n              Accept: 'application/json, application/jwk-set+json',\n            },\n          })\n          .finally(() => {\n            inFlight.delete(this);\n          });\n        const jwks = processResponse(response);\n\n        const joseKeyStore = KeyStore.fromJWKS(jwks, { onlyPublic: true });\n        cache.set('throttle', true, 60 * 1000);\n        keystores.set(this, joseKeyStore);\n\n        return joseKeyStore;\n      })(),\n    );\n\n    return inFlight.get(this);\n  }\n\n  return keystore;\n}\n\nasync function queryKeyStore({ kid, kty, alg, use }, { allowMulti = false } = {}) {\n  const cache = lrus(this);\n\n  const def = {\n    kid,\n    kty,\n    alg,\n    use,\n  };\n\n  const defHash = objectHash(def, {\n    algorithm: 'sha256',\n    ignoreUnknown: true,\n    unorderedArrays: true,\n    unorderedSets: true,\n    respectType: false,\n  });\n\n  // refresh keystore on every unknown key but also only upto once every minute\n  const freshJwksUri = cache.get(defHash) || cache.get('throttle');\n\n  const keystore = await getKeyStore.call(this, !freshJwksUri);\n  const keys = keystore.all(def);\n\n  delete def.use;\n  if (keys.length === 0) {\n    throw new RPError({\n      printf: [\"no valid key found in issuer's jwks_uri for key parameters %j\", def],\n      jwks: keystore,\n    });\n  }\n\n  if (!allowMulti && keys.length > 1 && !kid) {\n    throw new RPError({\n      printf: [\n        \"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\",\n        def,\n      ],\n      jwks: keystore,\n    });\n  }\n\n  cache.set(defHash, true);\n\n  return keys;\n}\n\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AAEN,MAAM,EAAE,OAAO,EAAE;AAEjB,MAAM,EAAE,yBAAyB,EAAE;AACnC,MAAM;AACN,MAAM,EAAE,SAAS,EAAE;AACnB,MAAM;AACN,MAAM;AAEN,MAAM,WAAW,IAAI;AACrB,MAAM,SAAS,IAAI;AACnB,MAAM,OAAO,CAAC;IACZ,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM;QACpB,OAAO,GAAG,CAAC,KAAK,IAAI,IAAI;YAAE,KAAK;QAAI;IACrC;IACA,OAAO,OAAO,GAAG,CAAC;AACpB;AAEA,eAAe,YAAY,SAAS,KAAK;IACvC,0BAA0B,IAAI,EAAE;IAEhC,MAAM,WAAW,UAAU,GAAG,CAAC,IAAI;IACnC,MAAM,QAAQ,KAAK,IAAI;IAEvB,IAAI,UAAU,CAAC,UAAU;QACvB,IAAI,SAAS,GAAG,CAAC,IAAI,GAAG;YACtB,OAAO,SAAS,GAAG,CAAC,IAAI;QAC1B;QACA,MAAM,KAAK;QACX,SAAS,GAAG,CACV,IAAI,EACJ,CAAC;YACC,MAAM,WAAW,MAAM,QACpB,IAAI,CAAC,IAAI,EAAE;gBACV,QAAQ;gBACR,cAAc;gBACd,KAAK,IAAI,CAAC,QAAQ;gBAClB,SAAS;oBACP,QAAQ;gBACV;YACF,GACC,OAAO,CAAC;gBACP,SAAS,MAAM,CAAC,IAAI;YACtB;YACF,MAAM,OAAO,gBAAgB;YAE7B,MAAM,eAAe,SAAS,QAAQ,CAAC,MAAM;gBAAE,YAAY;YAAK;YAChE,MAAM,GAAG,CAAC,YAAY,MAAM,KAAK;YACjC,UAAU,GAAG,CAAC,IAAI,EAAE;YAEpB,OAAO;QACT,CAAC;QAGH,OAAO,SAAS,GAAG,CAAC,IAAI;IAC1B;IAEA,OAAO;AACT;AAEA,eAAe,cAAc,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,aAAa,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9E,MAAM,QAAQ,KAAK,IAAI;IAEvB,MAAM,MAAM;QACV;QACA;QACA;QACA;IACF;IAEA,MAAM,UAAU,WAAW,KAAK;QAC9B,WAAW;QACX,eAAe;QACf,iBAAiB;QACjB,eAAe;QACf,aAAa;IACf;IAEA,6EAA6E;IAC7E,MAAM,eAAe,MAAM,GAAG,CAAC,YAAY,MAAM,GAAG,CAAC;IAErD,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,IAAI,EAAE,CAAC;IAC/C,MAAM,OAAO,SAAS,GAAG,CAAC;IAE1B,OAAO,IAAI,GAAG;IACd,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,MAAM,IAAI,QAAQ;YAChB,QAAQ;gBAAC;gBAAiE;aAAI;YAC9E,MAAM;QACR;IACF;IAEA,IAAI,CAAC,cAAc,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK;QAC1C,MAAM,IAAI,QAAQ;YAChB,QAAQ;gBACN;gBACA;aACD;YACD,MAAM;QACR;IACF;IAEA,MAAM,GAAG,CAAC,SAAS;IAEnB,OAAO;AACT;AAEA,OAAO,OAAO,CAAC,aAAa,GAAG;AAC/B,OAAO,OAAO,CAAC,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/device_flow_handle.js"], "sourcesContent": ["const { inspect } = require('util');\n\nconst { RPError, OPError } = require('./errors');\nconst now = require('./helpers/unix_timestamp');\n\nclass DeviceFlowHandle {\n  #aborted;\n  #client;\n  #clientAssertionPayload;\n  #DPoP;\n  #exchangeBody;\n  #expires_at;\n  #interval;\n  #maxAge;\n  #response;\n  constructor({ client, exchangeBody, clientAssertionPayload, response, maxAge, DPoP }) {\n    ['verification_uri', 'user_code', 'device_code'].forEach((prop) => {\n      if (typeof response[prop] !== 'string' || !response[prop]) {\n        throw new RPError(\n          `expected ${prop} string to be returned by Device Authorization Response, got %j`,\n          response[prop],\n        );\n      }\n    });\n\n    if (!Number.isSafeInteger(response.expires_in)) {\n      throw new RPError(\n        'expected expires_in number to be returned by Device Authorization Response, got %j',\n        response.expires_in,\n      );\n    }\n\n    this.#expires_at = now() + response.expires_in;\n    this.#client = client;\n    this.#DPoP = DPoP;\n    this.#maxAge = maxAge;\n    this.#exchangeBody = exchangeBody;\n    this.#clientAssertionPayload = clientAssertionPayload;\n    this.#response = response;\n    this.#interval = response.interval * 1000 || 5000;\n  }\n\n  abort() {\n    this.#aborted = true;\n  }\n\n  async poll({ signal } = {}) {\n    if ((signal && signal.aborted) || this.#aborted) {\n      throw new RPError('polling aborted');\n    }\n\n    if (this.expired()) {\n      throw new RPError(\n        'the device code %j has expired and the device authorization session has concluded',\n        this.device_code,\n      );\n    }\n\n    await new Promise((resolve) => setTimeout(resolve, this.#interval));\n\n    let tokenset;\n    try {\n      tokenset = await this.#client.grant(\n        {\n          ...this.#exchangeBody,\n          grant_type: 'urn:ietf:params:oauth:grant-type:device_code',\n          device_code: this.device_code,\n        },\n        { clientAssertionPayload: this.#clientAssertionPayload, DPoP: this.#DPoP },\n      );\n    } catch (err) {\n      switch (err instanceof OPError && err.error) {\n        case 'slow_down':\n          this.#interval += 5000;\n        case 'authorization_pending':\n          return this.poll({ signal });\n        default:\n          throw err;\n      }\n    }\n\n    if ('id_token' in tokenset) {\n      await this.#client.decryptIdToken(tokenset);\n      await this.#client.validateIdToken(tokenset, undefined, 'token', this.#maxAge);\n    }\n\n    return tokenset;\n  }\n\n  get device_code() {\n    return this.#response.device_code;\n  }\n\n  get user_code() {\n    return this.#response.user_code;\n  }\n\n  get verification_uri() {\n    return this.#response.verification_uri;\n  }\n\n  get verification_uri_complete() {\n    return this.#response.verification_uri_complete;\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.#expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.#response, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nmodule.exports = DeviceFlowHandle;\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,OAAO,EAAE;AAEjB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;AAC1B,MAAM;AAEN,MAAM;IACJ,CAAA,OAAQ,CAAC;IACT,CAAA,MAAO,CAAC;IACR,CAAA,sBAAuB,CAAC;IACxB,CAAA,IAAK,CAAC;IACN,CAAA,YAAa,CAAC;IACd,CAAA,UAAW,CAAC;IACZ,CAAA,QAAS,CAAC;IACV,CAAA,MAAO,CAAC;IACR,CAAA,QAAS,CAAC;IACV,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,sBAAsB,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,CAAE;QACpF;YAAC;YAAoB;YAAa;SAAc,CAAC,OAAO,CAAC,CAAC;YACxD,IAAI,OAAO,QAAQ,CAAC,KAAK,KAAK,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACzD,MAAM,IAAI,QACR,CAAC,SAAS,EAAE,KAAK,+DAA+D,CAAC,EACjF,QAAQ,CAAC,KAAK;YAElB;QACF;QAEA,IAAI,CAAC,OAAO,aAAa,CAAC,SAAS,UAAU,GAAG;YAC9C,MAAM,IAAI,QACR,sFACA,SAAS,UAAU;QAEvB;QAEA,IAAI,CAAC,CAAA,UAAW,GAAG,QAAQ,SAAS,UAAU;QAC9C,IAAI,CAAC,CAAA,MAAO,GAAG;QACf,IAAI,CAAC,CAAA,IAAK,GAAG;QACb,IAAI,CAAC,CAAA,MAAO,GAAG;QACf,IAAI,CAAC,CAAA,YAAa,GAAG;QACrB,IAAI,CAAC,CAAA,sBAAuB,GAAG;QAC/B,IAAI,CAAC,CAAA,QAAS,GAAG;QACjB,IAAI,CAAC,CAAA,QAAS,GAAG,SAAS,QAAQ,GAAG,QAAQ;IAC/C;IAEA,QAAQ;QACN,IAAI,CAAC,CAAA,OAAQ,GAAG;IAClB;IAEA,MAAM,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE;QAC1B,IAAI,AAAC,UAAU,OAAO,OAAO,IAAK,IAAI,CAAC,CAAA,OAAQ,EAAE;YAC/C,MAAM,IAAI,QAAQ;QACpB;QAEA,IAAI,IAAI,CAAC,OAAO,IAAI;YAClB,MAAM,IAAI,QACR,qFACA,IAAI,CAAC,WAAW;QAEpB;QAEA,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS,IAAI,CAAC,CAAA,QAAS;QAEjE,IAAI;QACJ,IAAI;YACF,WAAW,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,KAAK,CACjC;gBACE,GAAG,IAAI,CAAC,CAAA,YAAa;gBACrB,YAAY;gBACZ,aAAa,IAAI,CAAC,WAAW;YAC/B,GACA;gBAAE,wBAAwB,IAAI,CAAC,CAAA,sBAAuB;gBAAE,MAAM,IAAI,CAAC,CAAA,IAAK;YAAC;QAE7E,EAAE,OAAO,KAAK;YACZ,OAAQ,eAAe,WAAW,IAAI,KAAK;gBACzC,KAAK;oBACH,IAAI,CAAC,CAAA,QAAS,IAAI;gBACpB,KAAK;oBACH,OAAO,IAAI,CAAC,IAAI,CAAC;wBAAE;oBAAO;gBAC5B;oBACE,MAAM;YACV;QACF;QAEA,IAAI,cAAc,UAAU;YAC1B,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,cAAc,CAAC;YAClC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,eAAe,CAAC,UAAU,WAAW,SAAS,IAAI,CAAC,CAAA,MAAO;QAC/E;QAEA,OAAO;IACT;IAEA,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,WAAW;IACnC;IAEA,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,SAAS;IACjC;IAEA,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,gBAAgB;IACxC;IAEA,IAAI,4BAA4B;QAC9B,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,yBAAyB;IACjD;IAEA,IAAI,aAAa;QACf,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM;YAAC,IAAI,CAAC,CAAA,UAAW,GAAG;YAAO;SAAE;IAC3D;IAEA,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,KAAK;IAC7B;IAEA,wBAAwB,GACxB,CAAC,QAAQ,MAAM,CAAC,GAAG;QACjB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,CAAA,QAAS,EAAE;YACzD,OAAO;YACP,QAAQ,QAAQ,MAAM,CAAC,KAAK;YAC5B,SAAS;YACT,QAAQ;QACV,IAAI;IACN;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/client.js"], "sourcesContent": ["const { inspect } = require('util');\nconst stdhttp = require('http');\nconst crypto = require('crypto');\nconst { strict: assert } = require('assert');\nconst querystring = require('querystring');\nconst url = require('url');\nconst { URL, URLSearchParams } = require('url');\n\nconst jose = require('jose');\nconst tokenHash = require('oidc-token-hash');\n\nconst isKeyObject = require('./helpers/is_key_object');\nconst decodeJWT = require('./helpers/decode_jwt');\nconst base64url = require('./helpers/base64url');\nconst defaults = require('./helpers/defaults');\nconst parseWwwAuthenticate = require('./helpers/www_authenticate_parser');\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = require('./helpers/assert');\nconst pick = require('./helpers/pick');\nconst isPlainObject = require('./helpers/is_plain_object');\nconst processResponse = require('./helpers/process_response');\nconst TokenSet = require('./token_set');\nconst { OPError, RPError } = require('./errors');\nconst now = require('./helpers/unix_timestamp');\nconst { random } = require('./helpers/generators');\nconst request = require('./helpers/request');\nconst { CLOCK_TOLERANCE } = require('./helpers/consts');\nconst { keystores } = require('./helpers/weak_cache');\nconst KeyStore = require('./helpers/keystore');\nconst clone = require('./helpers/deep_clone');\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = require('./helpers/client');\nconst { queryKeyStore } = require('./helpers/issuer');\nconst DeviceFlowHandle = require('./device_flow_handle');\n\nconst [major, minor] = process.version\n  .slice(1)\n  .split('.')\n  .map((str) => parseInt(str, 10));\n\nconst rsaPssParams = major >= 17 || (major === 16 && minor >= 9);\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\n\nfunction pickCb(input) {\n  return pick(\n    input,\n    'access_token', // OAuth 2.0\n    'code', // OAuth 2.0\n    'error_description', // OAuth 2.0\n    'error_uri', // OAuth 2.0\n    'error', // OAuth 2.0\n    'expires_in', // OAuth 2.0\n    'id_token', // OIDC Core 1.0\n    'iss', // draft-ietf-oauth-iss-auth-resp\n    'response', // FAPI JARM\n    'session_state', // OIDC Session Management\n    'state', // OAuth 2.0\n    'token_type', // OAuth 2.0\n  );\n}\n\nfunction authorizationHeaderValue(token, tokenType = 'Bearer') {\n  return `${tokenType} ${token}`;\n}\n\nfunction getSearchParams(input) {\n  const parsed = url.parse(input);\n  if (!parsed.search) return {};\n  return querystring.parse(parsed.search.substring(1));\n}\n\nfunction verifyPresence(payload, jwt, prop) {\n  if (payload[prop] === undefined) {\n    throw new RPError({\n      message: `missing required JWT property ${prop}`,\n      jwt,\n    });\n  }\n}\n\nfunction authorizationParams(params) {\n  const authParams = {\n    client_id: this.client_id,\n    scope: 'openid',\n    response_type: resolveResponseType.call(this),\n    redirect_uri: resolveRedirectUri.call(this),\n    ...params,\n  };\n\n  Object.entries(authParams).forEach(([key, value]) => {\n    if (value === null || value === undefined) {\n      delete authParams[key];\n    } else if (key === 'claims' && typeof value === 'object') {\n      authParams[key] = JSON.stringify(value);\n    } else if (key === 'resource' && Array.isArray(value)) {\n      authParams[key] = value;\n    } else if (typeof value !== 'string') {\n      authParams[key] = String(value);\n    }\n  });\n\n  return authParams;\n}\n\nfunction getKeystore(jwks) {\n  if (\n    !isPlainObject(jwks) ||\n    !Array.isArray(jwks.keys) ||\n    jwks.keys.some((k) => !isPlainObject(k) || !('kty' in k))\n  ) {\n    throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n  }\n\n  return KeyStore.fromJWKS(jwks, { onlyPrivate: true });\n}\n\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n  try {\n    const supported = client.issuer.token_endpoint_auth_methods_supported;\n    if (!supported.includes(properties.token_endpoint_auth_method)) {\n      if (supported.includes('client_secret_post')) {\n        properties.token_endpoint_auth_method = 'client_secret_post';\n      }\n    }\n  } catch (err) {}\n}\n\nfunction handleCommonMistakes(client, metadata, properties) {\n  if (!metadata.token_endpoint_auth_method) {\n    // if no explicit value was provided\n    checkBasicSupport(client, properties);\n  }\n\n  // :fp: c'mon people... RTFM\n  if (metadata.redirect_uri) {\n    if (metadata.redirect_uris) {\n      throw new TypeError('provide a redirect_uri or redirect_uris, not both');\n    }\n    properties.redirect_uris = [metadata.redirect_uri];\n    delete properties.redirect_uri;\n  }\n\n  if (metadata.response_type) {\n    if (metadata.response_types) {\n      throw new TypeError('provide a response_type or response_types, not both');\n    }\n    properties.response_types = [metadata.response_type];\n    delete properties.response_type;\n  }\n}\n\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n  if (!issuer[`${endpoint}_endpoint`]) return;\n\n  const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n  const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n\n  const eam = `${endpoint}_endpoint_auth_method`;\n  const easa = `${endpoint}_endpoint_auth_signing_alg`;\n\n  if (properties[eam] === undefined && properties[easa] === undefined) {\n    if (tokenEndpointAuthMethod !== undefined) {\n      properties[eam] = tokenEndpointAuthMethod;\n    }\n    if (tokenEndpointAuthSigningAlg !== undefined) {\n      properties[easa] = tokenEndpointAuthSigningAlg;\n    }\n  }\n}\n\nclass BaseClient {\n  #metadata;\n  #issuer;\n  #aadIssValidation;\n  #additionalAuthorizedParties;\n  constructor(issuer, aadIssValidation, metadata = {}, jwks, options) {\n    this.#metadata = new Map();\n    this.#issuer = issuer;\n    this.#aadIssValidation = aadIssValidation;\n\n    if (typeof metadata.client_id !== 'string' || !metadata.client_id) {\n      throw new TypeError('client_id is required');\n    }\n\n    const properties = {\n      grant_types: ['authorization_code'],\n      id_token_signed_response_alg: 'RS256',\n      authorization_signed_response_alg: 'RS256',\n      response_types: ['code'],\n      token_endpoint_auth_method: 'client_secret_basic',\n      ...(this.fapi1()\n        ? {\n            grant_types: ['authorization_code', 'implicit'],\n            id_token_signed_response_alg: 'PS256',\n            authorization_signed_response_alg: 'PS256',\n            response_types: ['code id_token'],\n            tls_client_certificate_bound_access_tokens: true,\n            token_endpoint_auth_method: undefined,\n          }\n        : undefined),\n      ...(this.fapi2()\n        ? {\n            id_token_signed_response_alg: 'PS256',\n            authorization_signed_response_alg: 'PS256',\n            token_endpoint_auth_method: undefined,\n          }\n        : undefined),\n      ...metadata,\n    };\n\n    if (this.fapi()) {\n      switch (properties.token_endpoint_auth_method) {\n        case 'self_signed_tls_client_auth':\n        case 'tls_client_auth':\n          break;\n        case 'private_key_jwt':\n          if (!jwks) {\n            throw new TypeError('jwks is required');\n          }\n          break;\n        case undefined:\n          throw new TypeError('token_endpoint_auth_method is required');\n        default:\n          throw new TypeError('invalid or unsupported token_endpoint_auth_method');\n      }\n    }\n\n    if (this.fapi2()) {\n      if (\n        properties.tls_client_certificate_bound_access_tokens &&\n        properties.dpop_bound_access_tokens\n      ) {\n        throw new TypeError(\n          'either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true',\n        );\n      }\n\n      if (\n        !properties.tls_client_certificate_bound_access_tokens &&\n        !properties.dpop_bound_access_tokens\n      ) {\n        throw new TypeError(\n          'either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true',\n        );\n      }\n    }\n\n    handleCommonMistakes(this, metadata, properties);\n\n    assertSigningAlgValuesSupport('token', this.issuer, properties);\n    ['introspection', 'revocation'].forEach((endpoint) => {\n      getDefaultsForEndpoint(endpoint, this.issuer, properties);\n      assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n    });\n\n    Object.entries(properties).forEach(([key, value]) => {\n      this.#metadata.set(key, value);\n      if (!this[key]) {\n        Object.defineProperty(this, key, {\n          get() {\n            return this.#metadata.get(key);\n          },\n          enumerable: true,\n        });\n      }\n    });\n\n    if (jwks !== undefined) {\n      const keystore = getKeystore.call(this, jwks);\n      keystores.set(this, keystore);\n    }\n\n    if (options != null && options.additionalAuthorizedParties) {\n      this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n    }\n\n    this[CLOCK_TOLERANCE] = 0;\n  }\n\n  authorizationUrl(params = {}) {\n    if (!isPlainObject(params)) {\n      throw new TypeError('params must be a plain object');\n    }\n    assertIssuerConfiguration(this.issuer, 'authorization_endpoint');\n    const target = new URL(this.issuer.authorization_endpoint);\n\n    for (const [name, value] of Object.entries(authorizationParams.call(this, params))) {\n      if (Array.isArray(value)) {\n        target.searchParams.delete(name);\n        for (const member of value) {\n          target.searchParams.append(name, member);\n        }\n      } else {\n        target.searchParams.set(name, value);\n      }\n    }\n\n    // TODO: is the replace needed?\n    return target.href.replace(/\\+/g, '%20');\n  }\n\n  authorizationPost(params = {}) {\n    if (!isPlainObject(params)) {\n      throw new TypeError('params must be a plain object');\n    }\n    const inputs = authorizationParams.call(this, params);\n    const formInputs = Object.keys(inputs)\n      .map((name) => `<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`)\n      .join('\\n');\n\n    return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n  }\n\n  endSessionUrl(params = {}) {\n    assertIssuerConfiguration(this.issuer, 'end_session_endpoint');\n\n    const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n\n    const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n\n    let id_token_hint;\n    ({ id_token_hint, ...params } = params);\n    if (id_token_hint instanceof TokenSet) {\n      if (!id_token_hint.id_token) {\n        throw new TypeError('id_token not present in TokenSet');\n      }\n      id_token_hint = id_token_hint.id_token;\n    }\n\n    const target = url.parse(this.issuer.end_session_endpoint);\n    const query = defaults(\n      getSearchParams(this.issuer.end_session_endpoint),\n      params,\n      {\n        post_logout_redirect_uri,\n        client_id: this.client_id,\n      },\n      { id_token_hint },\n    );\n\n    Object.entries(query).forEach(([key, value]) => {\n      if (value === null || value === undefined) {\n        delete query[key];\n      }\n    });\n\n    target.search = null;\n    target.query = query;\n\n    return url.format(target);\n  }\n\n  callbackParams(input) {\n    const isIncomingMessage =\n      input instanceof stdhttp.IncomingMessage || (input && input.method && input.url);\n    const isString = typeof input === 'string';\n\n    if (!isString && !isIncomingMessage) {\n      throw new TypeError(\n        '#callbackParams only accepts string urls, http.IncomingMessage or a lookalike',\n      );\n    }\n    if (isIncomingMessage) {\n      switch (input.method) {\n        case 'GET':\n          return pickCb(getSearchParams(input.url));\n        case 'POST':\n          if (input.body === undefined) {\n            throw new TypeError(\n              'incoming message body missing, include a body parser prior to this method call',\n            );\n          }\n          switch (typeof input.body) {\n            case 'object':\n            case 'string':\n              if (Buffer.isBuffer(input.body)) {\n                return pickCb(querystring.parse(input.body.toString('utf-8')));\n              }\n              if (typeof input.body === 'string') {\n                return pickCb(querystring.parse(input.body));\n              }\n\n              return pickCb(input.body);\n            default:\n              throw new TypeError('invalid IncomingMessage body object');\n          }\n        default:\n          throw new TypeError('invalid IncomingMessage method');\n      }\n    } else {\n      return pickCb(getSearchParams(input));\n    }\n  }\n\n  async callback(\n    redirectUri,\n    parameters,\n    checks = {},\n    { exchangeBody, clientAssertionPayload, DPoP } = {},\n  ) {\n    let params = pickCb(parameters);\n\n    if (checks.jarm && !('response' in parameters)) {\n      throw new RPError({\n        message: 'expected a JARM response',\n        checks,\n        params,\n      });\n    } else if ('response' in parameters) {\n      const decrypted = await this.decryptJARM(params.response);\n      params = await this.validateJARM(decrypted);\n    }\n\n    if (this.default_max_age && !checks.max_age) {\n      checks.max_age = this.default_max_age;\n    }\n\n    if (params.state && !checks.state) {\n      throw new TypeError('checks.state argument is missing');\n    }\n\n    if (!params.state && checks.state) {\n      throw new RPError({\n        message: 'state missing from the response',\n        checks,\n        params,\n      });\n    }\n\n    if (checks.state !== params.state) {\n      throw new RPError({\n        printf: ['state mismatch, expected %s, got: %s', checks.state, params.state],\n        checks,\n        params,\n      });\n    }\n\n    if ('iss' in params) {\n      assertIssuerConfiguration(this.issuer, 'issuer');\n      if (params.iss !== this.issuer.issuer) {\n        throw new RPError({\n          printf: ['iss mismatch, expected %s, got: %s', this.issuer.issuer, params.iss],\n          params,\n        });\n      }\n    } else if (\n      this.issuer.authorization_response_iss_parameter_supported &&\n      !('id_token' in params) &&\n      !('response' in parameters)\n    ) {\n      throw new RPError({\n        message: 'iss missing from the response',\n        params,\n      });\n    }\n\n    if (params.error) {\n      throw new OPError(params);\n    }\n\n    const RESPONSE_TYPE_REQUIRED_PARAMS = {\n      code: ['code'],\n      id_token: ['id_token'],\n      token: ['access_token', 'token_type'],\n    };\n\n    if (checks.response_type) {\n      for (const type of checks.response_type.split(' ')) {\n        if (type === 'none') {\n          if (params.code || params.id_token || params.access_token) {\n            throw new RPError({\n              message: 'unexpected params encountered for \"none\" response',\n              checks,\n              params,\n            });\n          }\n        } else {\n          for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n            if (!params[param]) {\n              throw new RPError({\n                message: `${param} missing from response`,\n                checks,\n                params,\n              });\n            }\n          }\n        }\n      }\n    }\n\n    if (params.id_token) {\n      const tokenset = new TokenSet(params);\n      await this.decryptIdToken(tokenset);\n      await this.validateIdToken(\n        tokenset,\n        checks.nonce,\n        'authorization',\n        checks.max_age,\n        checks.state,\n      );\n\n      if (!params.code) {\n        return tokenset;\n      }\n    }\n\n    if (params.code) {\n      const tokenset = await this.grant(\n        {\n          ...exchangeBody,\n          grant_type: 'authorization_code',\n          code: params.code,\n          redirect_uri: redirectUri,\n          code_verifier: checks.code_verifier,\n        },\n        { clientAssertionPayload, DPoP },\n      );\n\n      await this.decryptIdToken(tokenset);\n      await this.validateIdToken(tokenset, checks.nonce, 'token', checks.max_age);\n\n      if (params.session_state) {\n        tokenset.session_state = params.session_state;\n      }\n\n      return tokenset;\n    }\n\n    return new TokenSet(params);\n  }\n\n  async oauthCallback(\n    redirectUri,\n    parameters,\n    checks = {},\n    { exchangeBody, clientAssertionPayload, DPoP } = {},\n  ) {\n    let params = pickCb(parameters);\n\n    if (checks.jarm && !('response' in parameters)) {\n      throw new RPError({\n        message: 'expected a JARM response',\n        checks,\n        params,\n      });\n    } else if ('response' in parameters) {\n      const decrypted = await this.decryptJARM(params.response);\n      params = await this.validateJARM(decrypted);\n    }\n\n    if (params.state && !checks.state) {\n      throw new TypeError('checks.state argument is missing');\n    }\n\n    if (!params.state && checks.state) {\n      throw new RPError({\n        message: 'state missing from the response',\n        checks,\n        params,\n      });\n    }\n\n    if (checks.state !== params.state) {\n      throw new RPError({\n        printf: ['state mismatch, expected %s, got: %s', checks.state, params.state],\n        checks,\n        params,\n      });\n    }\n\n    if ('iss' in params) {\n      assertIssuerConfiguration(this.issuer, 'issuer');\n      if (params.iss !== this.issuer.issuer) {\n        throw new RPError({\n          printf: ['iss mismatch, expected %s, got: %s', this.issuer.issuer, params.iss],\n          params,\n        });\n      }\n    } else if (\n      this.issuer.authorization_response_iss_parameter_supported &&\n      !('id_token' in params) &&\n      !('response' in parameters)\n    ) {\n      throw new RPError({\n        message: 'iss missing from the response',\n        params,\n      });\n    }\n\n    if (params.error) {\n      throw new OPError(params);\n    }\n\n    if (typeof params.id_token === 'string' && params.id_token.length) {\n      throw new RPError({\n        message:\n          'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n        params,\n      });\n    }\n    delete params.id_token;\n\n    const RESPONSE_TYPE_REQUIRED_PARAMS = {\n      code: ['code'],\n      token: ['access_token', 'token_type'],\n    };\n\n    if (checks.response_type) {\n      for (const type of checks.response_type.split(' ')) {\n        if (type === 'none') {\n          if (params.code || params.id_token || params.access_token) {\n            throw new RPError({\n              message: 'unexpected params encountered for \"none\" response',\n              checks,\n              params,\n            });\n          }\n        }\n\n        if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n          for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n            if (!params[param]) {\n              throw new RPError({\n                message: `${param} missing from response`,\n                checks,\n                params,\n              });\n            }\n          }\n        }\n      }\n    }\n\n    if (params.code) {\n      const tokenset = await this.grant(\n        {\n          ...exchangeBody,\n          grant_type: 'authorization_code',\n          code: params.code,\n          redirect_uri: redirectUri,\n          code_verifier: checks.code_verifier,\n        },\n        { clientAssertionPayload, DPoP },\n      );\n\n      if (typeof tokenset.id_token === 'string' && tokenset.id_token.length) {\n        throw new RPError({\n          message:\n            'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n          params,\n        });\n      }\n      delete tokenset.id_token;\n\n      return tokenset;\n    }\n\n    return new TokenSet(params);\n  }\n\n  async decryptIdToken(token) {\n    if (!this.id_token_encrypted_response_alg) {\n      return token;\n    }\n\n    let idToken = token;\n\n    if (idToken instanceof TokenSet) {\n      if (!idToken.id_token) {\n        throw new TypeError('id_token not present in TokenSet');\n      }\n      idToken = idToken.id_token;\n    }\n\n    const expectedAlg = this.id_token_encrypted_response_alg;\n    const expectedEnc = this.id_token_encrypted_response_enc;\n\n    const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n\n    if (token instanceof TokenSet) {\n      token.id_token = result;\n      return token;\n    }\n\n    return result;\n  }\n\n  async validateJWTUserinfo(body) {\n    const expectedAlg = this.userinfo_signed_response_alg;\n\n    return this.validateJWT(body, expectedAlg, []);\n  }\n\n  async decryptJARM(response) {\n    if (!this.authorization_encrypted_response_alg) {\n      return response;\n    }\n\n    const expectedAlg = this.authorization_encrypted_response_alg;\n    const expectedEnc = this.authorization_encrypted_response_enc;\n\n    return this.decryptJWE(response, expectedAlg, expectedEnc);\n  }\n\n  async decryptJWTUserinfo(body) {\n    if (!this.userinfo_encrypted_response_alg) {\n      return body;\n    }\n\n    const expectedAlg = this.userinfo_encrypted_response_alg;\n    const expectedEnc = this.userinfo_encrypted_response_enc;\n\n    return this.decryptJWE(body, expectedAlg, expectedEnc);\n  }\n\n  async decryptJWE(jwe, expectedAlg, expectedEnc = 'A128CBC-HS256') {\n    const header = JSON.parse(base64url.decode(jwe.split('.')[0]));\n\n    if (header.alg !== expectedAlg) {\n      throw new RPError({\n        printf: ['unexpected JWE alg received, expected %s, got: %s', expectedAlg, header.alg],\n        jwt: jwe,\n      });\n    }\n\n    if (header.enc !== expectedEnc) {\n      throw new RPError({\n        printf: ['unexpected JWE enc received, expected %s, got: %s', expectedEnc, header.enc],\n        jwt: jwe,\n      });\n    }\n\n    const getPlaintext = (result) => new TextDecoder().decode(result.plaintext);\n    let plaintext;\n    if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n      const keystore = await keystores.get(this);\n\n      const protectedHeader = jose.decodeProtectedHeader(jwe);\n\n      for (const key of keystore.all({\n        ...protectedHeader,\n        use: 'enc',\n      })) {\n        plaintext = await jose\n          .compactDecrypt(jwe, await key.keyObject(protectedHeader.alg))\n          .then(getPlaintext, () => {});\n        if (plaintext) break;\n      }\n    } else {\n      plaintext = await jose\n        .compactDecrypt(jwe, this.secretForAlg(expectedAlg === 'dir' ? expectedEnc : expectedAlg))\n        .then(getPlaintext, () => {});\n    }\n\n    if (!plaintext) {\n      throw new RPError({\n        message: 'failed to decrypt JWE',\n        jwt: jwe,\n      });\n    }\n    return plaintext;\n  }\n\n  async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n    let idToken = tokenSet;\n\n    const expectedAlg = this.id_token_signed_response_alg;\n\n    const isTokenSet = idToken instanceof TokenSet;\n\n    if (isTokenSet) {\n      if (!idToken.id_token) {\n        throw new TypeError('id_token not present in TokenSet');\n      }\n      idToken = idToken.id_token;\n    }\n\n    idToken = String(idToken);\n\n    const timestamp = now();\n    const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n\n    if (typeof maxAge === 'number' || (maxAge !== skipMaxAgeCheck && this.require_auth_time)) {\n      if (!payload.auth_time) {\n        throw new RPError({\n          message: 'missing required JWT property auth_time',\n          jwt: idToken,\n        });\n      }\n      if (typeof payload.auth_time !== 'number') {\n        throw new RPError({\n          message: 'JWT auth_time claim must be a JSON numeric value',\n          jwt: idToken,\n        });\n      }\n    }\n\n    if (\n      typeof maxAge === 'number' &&\n      payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]\n    ) {\n      throw new RPError({\n        printf: [\n          'too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i',\n          maxAge,\n          payload.auth_time,\n          timestamp - this[CLOCK_TOLERANCE],\n        ],\n        now: timestamp,\n        tolerance: this[CLOCK_TOLERANCE],\n        auth_time: payload.auth_time,\n        jwt: idToken,\n      });\n    }\n\n    if (\n      nonce !== skipNonceCheck &&\n      (payload.nonce || nonce !== undefined) &&\n      payload.nonce !== nonce\n    ) {\n      throw new RPError({\n        printf: ['nonce mismatch, expected %s, got: %s', nonce, payload.nonce],\n        jwt: idToken,\n      });\n    }\n\n    if (returnedBy === 'authorization') {\n      if (!payload.at_hash && tokenSet.access_token) {\n        throw new RPError({\n          message: 'missing required property at_hash',\n          jwt: idToken,\n        });\n      }\n\n      if (!payload.c_hash && tokenSet.code) {\n        throw new RPError({\n          message: 'missing required property c_hash',\n          jwt: idToken,\n        });\n      }\n\n      if (this.fapi1()) {\n        if (!payload.s_hash && (tokenSet.state || state)) {\n          throw new RPError({\n            message: 'missing required property s_hash',\n            jwt: idToken,\n          });\n        }\n      }\n\n      if (payload.s_hash) {\n        if (!state) {\n          throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n        }\n\n        try {\n          tokenHash.validate(\n            { claim: 's_hash', source: 'state' },\n            payload.s_hash,\n            state,\n            header.alg,\n            key.jwk && key.jwk.crv,\n          );\n        } catch (err) {\n          throw new RPError({ message: err.message, jwt: idToken });\n        }\n      }\n    }\n\n    if (this.fapi() && payload.iat < timestamp - 3600) {\n      throw new RPError({\n        printf: ['JWT issued too far in the past, now %i, iat %i', timestamp, payload.iat],\n        now: timestamp,\n        tolerance: this[CLOCK_TOLERANCE],\n        iat: payload.iat,\n        jwt: idToken,\n      });\n    }\n\n    if (tokenSet.access_token && payload.at_hash !== undefined) {\n      try {\n        tokenHash.validate(\n          { claim: 'at_hash', source: 'access_token' },\n          payload.at_hash,\n          tokenSet.access_token,\n          header.alg,\n          key.jwk && key.jwk.crv,\n        );\n      } catch (err) {\n        throw new RPError({ message: err.message, jwt: idToken });\n      }\n    }\n\n    if (tokenSet.code && payload.c_hash !== undefined) {\n      try {\n        tokenHash.validate(\n          { claim: 'c_hash', source: 'code' },\n          payload.c_hash,\n          tokenSet.code,\n          header.alg,\n          key.jwk && key.jwk.crv,\n        );\n      } catch (err) {\n        throw new RPError({ message: err.message, jwt: idToken });\n      }\n    }\n\n    return tokenSet;\n  }\n\n  async validateJWT(jwt, expectedAlg, required = ['iss', 'sub', 'aud', 'exp', 'iat']) {\n    const isSelfIssued = this.issuer.issuer === 'https://self-issued.me';\n    const timestamp = now();\n    let header;\n    let payload;\n    try {\n      ({ header, payload } = decodeJWT(jwt, { complete: true }));\n    } catch (err) {\n      throw new RPError({\n        printf: ['failed to decode JWT (%s: %s)', err.name, err.message],\n        jwt,\n      });\n    }\n\n    if (header.alg !== expectedAlg) {\n      throw new RPError({\n        printf: ['unexpected JWT alg received, expected %s, got: %s', expectedAlg, header.alg],\n        jwt,\n      });\n    }\n\n    if (isSelfIssued) {\n      required = [...required, 'sub_jwk'];\n    }\n\n    required.forEach(verifyPresence.bind(undefined, payload, jwt));\n\n    if (payload.iss !== undefined) {\n      let expectedIss = this.issuer.issuer;\n\n      if (this.#aadIssValidation) {\n        expectedIss = this.issuer.issuer.replace('{tenantid}', payload.tid);\n      }\n\n      if (payload.iss !== expectedIss) {\n        throw new RPError({\n          printf: ['unexpected iss value, expected %s, got: %s', expectedIss, payload.iss],\n          jwt,\n        });\n      }\n    }\n\n    if (payload.iat !== undefined) {\n      if (typeof payload.iat !== 'number') {\n        throw new RPError({\n          message: 'JWT iat claim must be a JSON numeric value',\n          jwt,\n        });\n      }\n    }\n\n    if (payload.nbf !== undefined) {\n      if (typeof payload.nbf !== 'number') {\n        throw new RPError({\n          message: 'JWT nbf claim must be a JSON numeric value',\n          jwt,\n        });\n      }\n      if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n        throw new RPError({\n          printf: [\n            'JWT not active yet, now %i, nbf %i',\n            timestamp + this[CLOCK_TOLERANCE],\n            payload.nbf,\n          ],\n          now: timestamp,\n          tolerance: this[CLOCK_TOLERANCE],\n          nbf: payload.nbf,\n          jwt,\n        });\n      }\n    }\n\n    if (payload.exp !== undefined) {\n      if (typeof payload.exp !== 'number') {\n        throw new RPError({\n          message: 'JWT exp claim must be a JSON numeric value',\n          jwt,\n        });\n      }\n      if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n        throw new RPError({\n          printf: ['JWT expired, now %i, exp %i', timestamp - this[CLOCK_TOLERANCE], payload.exp],\n          now: timestamp,\n          tolerance: this[CLOCK_TOLERANCE],\n          exp: payload.exp,\n          jwt,\n        });\n      }\n    }\n\n    if (payload.aud !== undefined) {\n      if (Array.isArray(payload.aud)) {\n        if (payload.aud.length > 1 && !payload.azp) {\n          throw new RPError({\n            message: 'missing required JWT property azp',\n            jwt,\n          });\n        }\n\n        if (!payload.aud.includes(this.client_id)) {\n          throw new RPError({\n            printf: [\n              'aud is missing the client_id, expected %s to be included in %j',\n              this.client_id,\n              payload.aud,\n            ],\n            jwt,\n          });\n        }\n      } else if (payload.aud !== this.client_id) {\n        throw new RPError({\n          printf: ['aud mismatch, expected %s, got: %s', this.client_id, payload.aud],\n          jwt,\n        });\n      }\n    }\n\n    if (payload.azp !== undefined) {\n      let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n\n      if (typeof additionalAuthorizedParties === 'string') {\n        additionalAuthorizedParties = [this.client_id, additionalAuthorizedParties];\n      } else if (Array.isArray(additionalAuthorizedParties)) {\n        additionalAuthorizedParties = [this.client_id, ...additionalAuthorizedParties];\n      } else {\n        additionalAuthorizedParties = [this.client_id];\n      }\n\n      if (!additionalAuthorizedParties.includes(payload.azp)) {\n        throw new RPError({\n          printf: ['azp mismatch, got: %s', payload.azp],\n          jwt,\n        });\n      }\n    }\n\n    let keys;\n\n    if (isSelfIssued) {\n      try {\n        assert(isPlainObject(payload.sub_jwk));\n        const key = await jose.importJWK(payload.sub_jwk, header.alg);\n        assert.equal(key.type, 'public');\n        keys = [\n          {\n            keyObject() {\n              return key;\n            },\n          },\n        ];\n      } catch (err) {\n        throw new RPError({\n          message: 'failed to use sub_jwk claim as an asymmetric JSON Web Key',\n          jwt,\n        });\n      }\n      if ((await jose.calculateJwkThumbprint(payload.sub_jwk)) !== payload.sub) {\n        throw new RPError({\n          message: 'failed to match the subject with sub_jwk',\n          jwt,\n        });\n      }\n    } else if (header.alg.startsWith('HS')) {\n      keys = [this.secretForAlg(header.alg)];\n    } else if (header.alg !== 'none') {\n      keys = await queryKeyStore.call(this.issuer, { ...header, use: 'sig' });\n    }\n\n    if (!keys && header.alg === 'none') {\n      return { protected: header, payload };\n    }\n\n    for (const key of keys) {\n      const verified = await jose\n        .compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg))\n        .catch(() => {});\n      if (verified) {\n        return {\n          payload,\n          protected: verified.protectedHeader,\n          key,\n        };\n      }\n    }\n\n    throw new RPError({\n      message: 'failed to validate JWT signature',\n      jwt,\n    });\n  }\n\n  async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n    let token = refreshToken;\n\n    if (token instanceof TokenSet) {\n      if (!token.refresh_token) {\n        throw new TypeError('refresh_token not present in TokenSet');\n      }\n      token = token.refresh_token;\n    }\n\n    const tokenset = await this.grant(\n      {\n        ...exchangeBody,\n        grant_type: 'refresh_token',\n        refresh_token: String(token),\n      },\n      { clientAssertionPayload, DPoP },\n    );\n\n    if (tokenset.id_token) {\n      await this.decryptIdToken(tokenset);\n      await this.validateIdToken(tokenset, skipNonceCheck, 'token', skipMaxAgeCheck);\n\n      if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n        const expectedSub = refreshToken.claims().sub;\n        const actualSub = tokenset.claims().sub;\n        if (actualSub !== expectedSub) {\n          throw new RPError({\n            printf: ['sub mismatch, expected %s, got: %s', expectedSub, actualSub],\n            jwt: tokenset.id_token,\n          });\n        }\n      }\n    }\n\n    return tokenset;\n  }\n\n  async requestResource(\n    resourceUrl,\n    accessToken,\n    {\n      method,\n      headers,\n      body,\n      DPoP,\n      tokenType = DPoP\n        ? 'DPoP'\n        : accessToken instanceof TokenSet\n        ? accessToken.token_type\n        : 'Bearer',\n    } = {},\n    retry,\n  ) {\n    if (accessToken instanceof TokenSet) {\n      if (!accessToken.access_token) {\n        throw new TypeError('access_token not present in TokenSet');\n      }\n      accessToken = accessToken.access_token;\n    }\n\n    if (!accessToken) {\n      throw new TypeError('no access token provided');\n    } else if (typeof accessToken !== 'string') {\n      throw new TypeError('invalid access token provided');\n    }\n\n    const requestOpts = {\n      headers: {\n        Authorization: authorizationHeaderValue(accessToken, tokenType),\n        ...headers,\n      },\n      body,\n    };\n\n    const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n\n    const response = await request.call(\n      this,\n      {\n        ...requestOpts,\n        responseType: 'buffer',\n        method,\n        url: resourceUrl,\n      },\n      { accessToken, mTLS, DPoP },\n    );\n\n    const wwwAuthenticate = response.headers['www-authenticate'];\n    if (\n      retry !== retryAttempt &&\n      wwwAuthenticate &&\n      wwwAuthenticate.toLowerCase().startsWith('dpop ') &&\n      parseWwwAuthenticate(wwwAuthenticate).error === 'use_dpop_nonce'\n    ) {\n      return this.requestResource(resourceUrl, accessToken, {\n        method,\n        headers,\n        body,\n        DPoP,\n        tokenType,\n      });\n    }\n\n    return response;\n  }\n\n  async userinfo(accessToken, { method = 'GET', via = 'header', tokenType, params, DPoP } = {}) {\n    assertIssuerConfiguration(this.issuer, 'userinfo_endpoint');\n    const options = {\n      tokenType,\n      method: String(method).toUpperCase(),\n      DPoP,\n    };\n\n    if (options.method !== 'GET' && options.method !== 'POST') {\n      throw new TypeError('#userinfo() method can only be POST or a GET');\n    }\n\n    if (via === 'body' && options.method !== 'POST') {\n      throw new TypeError('can only send body on POST');\n    }\n\n    const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n\n    if (jwt) {\n      options.headers = { Accept: 'application/jwt' };\n    } else {\n      options.headers = { Accept: 'application/json' };\n    }\n    const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n\n    let targetUrl;\n    if (mTLS && this.issuer.mtls_endpoint_aliases) {\n      targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n    }\n\n    targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n\n    if (via === 'body') {\n      options.headers.Authorization = undefined;\n      options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n      options.body = new URLSearchParams();\n      options.body.append(\n        'access_token',\n        accessToken instanceof TokenSet ? accessToken.access_token : accessToken,\n      );\n    }\n\n    // handle additional parameters, GET via querystring, POST via urlencoded body\n    if (params) {\n      if (options.method === 'GET') {\n        Object.entries(params).forEach(([key, value]) => {\n          targetUrl.searchParams.append(key, value);\n        });\n      } else if (options.body) {\n        // POST && via body\n        Object.entries(params).forEach(([key, value]) => {\n          options.body.append(key, value);\n        });\n      } else {\n        // POST && via header\n        options.body = new URLSearchParams();\n        options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n        Object.entries(params).forEach(([key, value]) => {\n          options.body.append(key, value);\n        });\n      }\n    }\n\n    if (options.body) {\n      options.body = options.body.toString();\n    }\n\n    const response = await this.requestResource(targetUrl, accessToken, options);\n\n    let parsed = processResponse(response, { bearer: true });\n\n    if (jwt) {\n      if (!/^application\\/jwt/.test(response.headers['content-type'])) {\n        throw new RPError({\n          message: 'expected application/jwt response from the userinfo_endpoint',\n          response,\n        });\n      }\n\n      const body = response.body.toString();\n      const userinfo = await this.decryptJWTUserinfo(body);\n      if (!this.userinfo_signed_response_alg) {\n        try {\n          parsed = JSON.parse(userinfo);\n          assert(isPlainObject(parsed));\n        } catch (err) {\n          throw new RPError({\n            message: 'failed to parse userinfo JWE payload as JSON',\n            jwt: userinfo,\n          });\n        }\n      } else {\n        ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n      }\n    } else {\n      try {\n        parsed = JSON.parse(response.body);\n      } catch (err) {\n        Object.defineProperty(err, 'response', { value: response });\n        throw err;\n      }\n    }\n\n    if (accessToken instanceof TokenSet && accessToken.id_token) {\n      const expectedSub = accessToken.claims().sub;\n      if (parsed.sub !== expectedSub) {\n        throw new RPError({\n          printf: ['userinfo sub mismatch, expected %s, got: %s', expectedSub, parsed.sub],\n          body: parsed,\n          jwt: accessToken.id_token,\n        });\n      }\n    }\n\n    return parsed;\n  }\n\n  encryptionSecret(len) {\n    const hash = len <= 256 ? 'sha256' : len <= 384 ? 'sha384' : len <= 512 ? 'sha512' : false;\n    if (!hash) {\n      throw new Error('unsupported symmetric encryption key derivation');\n    }\n\n    return crypto\n      .createHash(hash)\n      .update(this.client_secret)\n      .digest()\n      .slice(0, len / 8);\n  }\n\n  secretForAlg(alg) {\n    if (!this.client_secret) {\n      throw new TypeError('client_secret is required');\n    }\n\n    if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n      return this.encryptionSecret(parseInt(RegExp.$1, 10));\n    }\n\n    if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n      return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n    }\n\n    return new TextEncoder().encode(this.client_secret);\n  }\n\n  async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n    assertIssuerConfiguration(this.issuer, 'token_endpoint');\n    const response = await authenticatedPost.call(\n      this,\n      'token',\n      {\n        form: body,\n        responseType: 'json',\n      },\n      { clientAssertionPayload, DPoP },\n    );\n    let responseBody;\n    try {\n      responseBody = processResponse(response);\n    } catch (err) {\n      if (retry !== retryAttempt && err instanceof OPError && err.error === 'use_dpop_nonce') {\n        return this.grant(body, { clientAssertionPayload, DPoP }, retryAttempt);\n      }\n      throw err;\n    }\n\n    return new TokenSet(responseBody);\n  }\n\n  async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n    assertIssuerConfiguration(this.issuer, 'device_authorization_endpoint');\n    assertIssuerConfiguration(this.issuer, 'token_endpoint');\n\n    const body = authorizationParams.call(this, {\n      client_id: this.client_id,\n      redirect_uri: null,\n      response_type: null,\n      ...params,\n    });\n\n    const response = await authenticatedPost.call(\n      this,\n      'device_authorization',\n      {\n        responseType: 'json',\n        form: body,\n      },\n      { clientAssertionPayload, endpointAuthMethod: 'token' },\n    );\n    const responseBody = processResponse(response);\n\n    return new DeviceFlowHandle({\n      client: this,\n      exchangeBody,\n      clientAssertionPayload,\n      response: responseBody,\n      maxAge: params.max_age,\n      DPoP,\n    });\n  }\n\n  async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n    assertIssuerConfiguration(this.issuer, 'revocation_endpoint');\n    if (hint !== undefined && typeof hint !== 'string') {\n      throw new TypeError('hint must be a string');\n    }\n\n    const form = { ...revokeBody, token };\n\n    if (hint) {\n      form.token_type_hint = hint;\n    }\n\n    const response = await authenticatedPost.call(\n      this,\n      'revocation',\n      {\n        form,\n      },\n      { clientAssertionPayload },\n    );\n    processResponse(response, { body: false });\n  }\n\n  async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n    assertIssuerConfiguration(this.issuer, 'introspection_endpoint');\n    if (hint !== undefined && typeof hint !== 'string') {\n      throw new TypeError('hint must be a string');\n    }\n\n    const form = { ...introspectBody, token };\n    if (hint) {\n      form.token_type_hint = hint;\n    }\n\n    const response = await authenticatedPost.call(\n      this,\n      'introspection',\n      { form, responseType: 'json' },\n      { clientAssertionPayload },\n    );\n\n    const responseBody = processResponse(response);\n\n    return responseBody;\n  }\n\n  static async register(metadata, options = {}) {\n    const { initialAccessToken, jwks, ...clientOptions } = options;\n\n    assertIssuerConfiguration(this.issuer, 'registration_endpoint');\n\n    if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n      const keystore = await getKeystore.call(this, jwks);\n      metadata.jwks = keystore.toJWKS();\n    }\n\n    const response = await request.call(this, {\n      headers: {\n        Accept: 'application/json',\n        ...(initialAccessToken\n          ? {\n              Authorization: authorizationHeaderValue(initialAccessToken),\n            }\n          : undefined),\n      },\n      responseType: 'json',\n      json: metadata,\n      url: this.issuer.registration_endpoint,\n      method: 'POST',\n    });\n    const responseBody = processResponse(response, { statusCode: 201, bearer: true });\n\n    return new this(responseBody, jwks, clientOptions);\n  }\n\n  get metadata() {\n    return clone(Object.fromEntries(this.#metadata.entries()));\n  }\n\n  static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n    const response = await request.call(this, {\n      method: 'GET',\n      url: registrationClientUri,\n      responseType: 'json',\n      headers: {\n        Authorization: authorizationHeaderValue(registrationAccessToken),\n        Accept: 'application/json',\n      },\n    });\n    const responseBody = processResponse(response, { bearer: true });\n\n    return new this(responseBody, jwks, clientOptions);\n  }\n\n  async requestObject(\n    requestObject = {},\n    {\n      sign: signingAlgorithm = this.request_object_signing_alg || 'none',\n      encrypt: {\n        alg: eKeyManagement = this.request_object_encryption_alg,\n        enc: eContentEncryption = this.request_object_encryption_enc || 'A128CBC-HS256',\n      } = {},\n    } = {},\n  ) {\n    if (!isPlainObject(requestObject)) {\n      throw new TypeError('requestObject must be a plain object');\n    }\n\n    let signed;\n    let key;\n    const unix = now();\n    const header = { alg: signingAlgorithm, typ: 'oauth-authz-req+jwt' };\n    const payload = JSON.stringify(\n      defaults({}, requestObject, {\n        iss: this.client_id,\n        aud: this.issuer.issuer,\n        client_id: this.client_id,\n        jti: random(),\n        iat: unix,\n        exp: unix + 300,\n        ...(this.fapi() ? { nbf: unix } : undefined),\n      }),\n    );\n    if (signingAlgorithm === 'none') {\n      signed = [base64url.encode(JSON.stringify(header)), base64url.encode(payload), ''].join('.');\n    } else {\n      const symmetric = signingAlgorithm.startsWith('HS');\n      if (symmetric) {\n        key = this.secretForAlg(signingAlgorithm);\n      } else {\n        const keystore = await keystores.get(this);\n\n        if (!keystore) {\n          throw new TypeError(\n            `no keystore present for client, cannot sign using alg ${signingAlgorithm}`,\n          );\n        }\n        key = keystore.get({ alg: signingAlgorithm, use: 'sig' });\n        if (!key) {\n          throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n        }\n      }\n\n      signed = await new jose.CompactSign(new TextEncoder().encode(payload))\n        .setProtectedHeader({\n          ...header,\n          kid: symmetric ? undefined : key.jwk.kid,\n        })\n        .sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n    }\n\n    if (!eKeyManagement) {\n      return signed;\n    }\n\n    const fields = { alg: eKeyManagement, enc: eContentEncryption, cty: 'oauth-authz-req+jwt' };\n\n    if (fields.alg.match(/^(RSA|ECDH)/)) {\n      [key] = await queryKeyStore.call(\n        this.issuer,\n        { alg: fields.alg, use: 'enc' },\n        { allowMulti: true },\n      );\n    } else {\n      key = this.secretForAlg(fields.alg === 'dir' ? fields.enc : fields.alg);\n    }\n\n    return new jose.CompactEncrypt(new TextEncoder().encode(signed))\n      .setProtectedHeader({\n        ...fields,\n        kid: key instanceof Uint8Array ? undefined : key.jwk.kid,\n      })\n      .encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n  }\n\n  async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n    assertIssuerConfiguration(this.issuer, 'pushed_authorization_request_endpoint');\n\n    const body = {\n      ...('request' in params ? params : authorizationParams.call(this, params)),\n      client_id: this.client_id,\n    };\n\n    const response = await authenticatedPost.call(\n      this,\n      'pushed_authorization_request',\n      {\n        responseType: 'json',\n        form: body,\n      },\n      { clientAssertionPayload, endpointAuthMethod: 'token' },\n    );\n    const responseBody = processResponse(response, { statusCode: 201 });\n\n    if (!('expires_in' in responseBody)) {\n      throw new RPError({\n        message: 'expected expires_in in Pushed Authorization Successful Response',\n        response,\n      });\n    }\n    if (typeof responseBody.expires_in !== 'number') {\n      throw new RPError({\n        message: 'invalid expires_in value in Pushed Authorization Successful Response',\n        response,\n      });\n    }\n    if (!('request_uri' in responseBody)) {\n      throw new RPError({\n        message: 'expected request_uri in Pushed Authorization Successful Response',\n        response,\n      });\n    }\n    if (typeof responseBody.request_uri !== 'string') {\n      throw new RPError({\n        message: 'invalid request_uri value in Pushed Authorization Successful Response',\n        response,\n      });\n    }\n\n    return responseBody;\n  }\n\n  get issuer() {\n    return this.#issuer;\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.metadata, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n\n  fapi() {\n    return this.fapi1() || this.fapi2();\n  }\n\n  fapi1() {\n    return this.constructor.name === 'FAPI1Client';\n  }\n\n  fapi2() {\n    return this.constructor.name === 'FAPI2Client';\n  }\n\n  async validateJARM(response) {\n    const expectedAlg = this.authorization_signed_response_alg;\n    const { payload } = await this.validateJWT(response, expectedAlg, ['iss', 'exp', 'aud']);\n    return pickCb(payload);\n  }\n\n  /**\n   * @name dpopProof\n   * @api private\n   */\n  async dpopProof(payload, privateKeyInput, accessToken) {\n    if (!isPlainObject(payload)) {\n      throw new TypeError('payload must be a plain object');\n    }\n\n    let privateKey;\n    if (isKeyObject(privateKeyInput)) {\n      privateKey = privateKeyInput;\n    } else if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n      privateKey = privateKeyInput;\n    } else if (jose.cryptoRuntime === 'node:crypto') {\n      privateKey = crypto.createPrivateKey(privateKeyInput);\n    } else {\n      throw new TypeError('unrecognized crypto runtime');\n    }\n\n    if (privateKey.type !== 'private') {\n      throw new TypeError('\"DPoP\" option must be a private key');\n    }\n    let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n\n    if (!alg) {\n      throw new TypeError('could not determine DPoP JWS Algorithm');\n    }\n\n    return new jose.SignJWT({\n      ath: accessToken\n        ? base64url.encode(crypto.createHash('sha256').update(accessToken).digest())\n        : undefined,\n      ...payload,\n    })\n      .setProtectedHeader({\n        alg,\n        typ: 'dpop+jwt',\n        jwk: await getJwk(privateKey, privateKeyInput),\n      })\n      .setIssuedAt()\n      .setJti(random())\n      .sign(privateKey);\n  }\n}\n\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n  switch (cryptoKey.algorithm.name) {\n    case 'Ed25519':\n    case 'Ed448':\n      return 'EdDSA';\n    case 'ECDSA': {\n      switch (cryptoKey.algorithm.namedCurve) {\n        case 'P-256':\n          return 'ES256';\n        case 'P-384':\n          return 'ES384';\n        case 'P-521':\n          return 'ES512';\n        default:\n          break;\n      }\n      break;\n    }\n    case 'RSASSA-PKCS1-v1_5':\n      return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n    case 'RSA-PSS':\n      return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n    default:\n      throw new TypeError('unsupported DPoP private key');\n  }\n}\n\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === 'node:crypto') {\n  determineDPoPAlgorithm = function (privateKey, privateKeyInput) {\n    if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n      return determineDPoPAlgorithmFromCryptoKey(privateKey);\n    }\n\n    switch (privateKey.asymmetricKeyType) {\n      case 'ed25519':\n      case 'ed448':\n        return 'EdDSA';\n      case 'ec':\n        return determineEcAlgorithm(privateKey, privateKeyInput);\n      case 'rsa':\n      case rsaPssParams && 'rsa-pss':\n        return determineRsaAlgorithm(\n          privateKey,\n          privateKeyInput,\n          this.issuer.dpop_signing_alg_values_supported,\n        );\n      default:\n        throw new TypeError('unsupported DPoP private key');\n    }\n  };\n\n  const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n  function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n    if (\n      typeof privateKeyInput === 'object' &&\n      privateKeyInput.format === 'jwk' &&\n      privateKeyInput.key &&\n      privateKeyInput.key.alg\n    ) {\n      return privateKeyInput.key.alg;\n    }\n\n    if (Array.isArray(valuesSupported)) {\n      let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n      if (privateKey.asymmetricKeyType === 'rsa-pss') {\n        candidates = candidates.filter((value) => value.startsWith('PS'));\n      }\n      return ['PS256', 'PS384', 'PS512', 'RS256', 'RS384', 'RS384'].find((preferred) =>\n        candidates.includes(preferred),\n      );\n    }\n\n    return 'PS256';\n  }\n\n  const p256 = Buffer.from([42, 134, 72, 206, 61, 3, 1, 7]);\n  const p384 = Buffer.from([43, 129, 4, 0, 34]);\n  const p521 = Buffer.from([43, 129, 4, 0, 35]);\n  const secp256k1 = Buffer.from([43, 129, 4, 0, 10]);\n\n  function determineEcAlgorithm(privateKey, privateKeyInput) {\n    // If input was a JWK\n    switch (\n      typeof privateKeyInput === 'object' &&\n      typeof privateKeyInput.key === 'object' &&\n      privateKeyInput.key.crv\n    ) {\n      case 'P-256':\n        return 'ES256';\n      case 'secp256k1':\n        return 'ES256K';\n      case 'P-384':\n        return 'ES384';\n      case 'P-512':\n        return 'ES512';\n      default:\n        break;\n    }\n\n    const buf = privateKey.export({ format: 'der', type: 'pkcs8' });\n    const i = buf[1] < 128 ? 17 : 18;\n    const len = buf[i];\n    const curveOid = buf.slice(i + 1, i + 1 + len);\n    if (curveOid.equals(p256)) {\n      return 'ES256';\n    }\n\n    if (curveOid.equals(p384)) {\n      return 'ES384';\n    }\n    if (curveOid.equals(p521)) {\n      return 'ES512';\n    }\n\n    if (curveOid.equals(secp256k1)) {\n      return 'ES256K';\n    }\n\n    throw new TypeError('unsupported DPoP private key curve');\n  }\n} else {\n  determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\n\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n  if (\n    jose.cryptoRuntime === 'node:crypto' &&\n    typeof privateKeyInput === 'object' &&\n    typeof privateKeyInput.key === 'object' &&\n    privateKeyInput.format === 'jwk'\n  ) {\n    return pick(privateKeyInput.key, 'kty', 'crv', 'x', 'y', 'e', 'n');\n  }\n\n  if (jwkCache.has(privateKeyInput)) {\n    return jwkCache.get(privateKeyInput);\n  }\n\n  const jwk = pick(await jose.exportJWK(keyObject), 'kty', 'crv', 'x', 'y', 'e', 'n');\n\n  if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === 'WebCryptoAPI') {\n    jwkCache.set(privateKeyInput, jwk);\n  }\n\n  return jwk;\n}\n\nmodule.exports = (issuer, aadIssValidation = false) =>\n  class Client extends BaseClient {\n    constructor(...args) {\n      super(issuer, aadIssValidation, ...args);\n    }\n\n    static get issuer() {\n      return issuer;\n    }\n  };\n\nmodule.exports.BaseClient = BaseClient;\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,OAAO,EAAE;AACjB,MAAM;AACN,MAAM;AACN,MAAM,EAAE,QAAQ,MAAM,EAAE;AACxB,MAAM;AACN,MAAM;AACN,MAAM,EAAE,GAAG,EAAE,eAAe,EAAE;AAE9B,MAAM;AACN,MAAM;AAEN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,6BAA6B,EAAE,yBAAyB,EAAE;AAClE,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;AAC1B,MAAM;AACN,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM;AACN,MAAM,EAAE,eAAe,EAAE;AACzB,MAAM,EAAE,SAAS,EAAE;AACnB,MAAM;AACN,MAAM;AACN,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE;AACpE,MAAM,EAAE,aAAa,EAAE;AACvB,MAAM;AAEN,MAAM,CAAC,OAAO,MAAM,GAAG,QAAQ,OAAO,CACnC,KAAK,CAAC,GACN,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,MAAQ,SAAS,KAAK;AAE9B,MAAM,eAAe,SAAS,MAAO,UAAU,MAAM,SAAS;AAC9D,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AAExB,SAAS,OAAO,KAAK;IACnB,OAAO,KACL,OACA,gBACA,QACA,qBACA,aACA,SACA,cACA,YACA,OACA,YACA,iBACA,SACA;AAEJ;AAEA,SAAS,yBAAyB,KAAK,EAAE,YAAY,QAAQ;IAC3D,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO;AAChC;AAEA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,SAAS,IAAI,KAAK,CAAC;IACzB,IAAI,CAAC,OAAO,MAAM,EAAE,OAAO,CAAC;IAC5B,OAAO,YAAY,KAAK,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC;AACnD;AAEA,SAAS,eAAe,OAAO,EAAE,GAAG,EAAE,IAAI;IACxC,IAAI,OAAO,CAAC,KAAK,KAAK,WAAW;QAC/B,MAAM,IAAI,QAAQ;YAChB,SAAS,CAAC,8BAA8B,EAAE,MAAM;YAChD;QACF;IACF;AACF;AAEA,SAAS,oBAAoB,MAAM;IACjC,MAAM,aAAa;QACjB,WAAW,IAAI,CAAC,SAAS;QACzB,OAAO;QACP,eAAe,oBAAoB,IAAI,CAAC,IAAI;QAC5C,cAAc,mBAAmB,IAAI,CAAC,IAAI;QAC1C,GAAG,MAAM;IACX;IAEA,OAAO,OAAO,CAAC,YAAY,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC9C,IAAI,UAAU,QAAQ,UAAU,WAAW;YACzC,OAAO,UAAU,CAAC,IAAI;QACxB,OAAO,IAAI,QAAQ,YAAY,OAAO,UAAU,UAAU;YACxD,UAAU,CAAC,IAAI,GAAG,KAAK,SAAS,CAAC;QACnC,OAAO,IAAI,QAAQ,cAAc,MAAM,OAAO,CAAC,QAAQ;YACrD,UAAU,CAAC,IAAI,GAAG;QACpB,OAAO,IAAI,OAAO,UAAU,UAAU;YACpC,UAAU,CAAC,IAAI,GAAG,OAAO;QAC3B;IACF;IAEA,OAAO;AACT;AAEA,SAAS,YAAY,IAAI;IACvB,IACE,CAAC,cAAc,SACf,CAAC,MAAM,OAAO,CAAC,KAAK,IAAI,KACxB,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAM,CAAC,cAAc,MAAM,CAAC,CAAC,SAAS,CAAC,IACvD;QACA,MAAM,IAAI,UAAU;IACtB;IAEA,OAAO,SAAS,QAAQ,CAAC,MAAM;QAAE,aAAa;IAAK;AACrD;AAEA,8FAA8F;AAC9F,oGAAoG;AACpG,0DAA0D;AAC1D,SAAS,kBAAkB,MAAM,EAAE,UAAU;IAC3C,IAAI;QACF,MAAM,YAAY,OAAO,MAAM,CAAC,qCAAqC;QACrE,IAAI,CAAC,UAAU,QAAQ,CAAC,WAAW,0BAA0B,GAAG;YAC9D,IAAI,UAAU,QAAQ,CAAC,uBAAuB;gBAC5C,WAAW,0BAA0B,GAAG;YAC1C;QACF;IACF,EAAE,OAAO,KAAK,CAAC;AACjB;AAEA,SAAS,qBAAqB,MAAM,EAAE,QAAQ,EAAE,UAAU;IACxD,IAAI,CAAC,SAAS,0BAA0B,EAAE;QACxC,oCAAoC;QACpC,kBAAkB,QAAQ;IAC5B;IAEA,4BAA4B;IAC5B,IAAI,SAAS,YAAY,EAAE;QACzB,IAAI,SAAS,aAAa,EAAE;YAC1B,MAAM,IAAI,UAAU;QACtB;QACA,WAAW,aAAa,GAAG;YAAC,SAAS,YAAY;SAAC;QAClD,OAAO,WAAW,YAAY;IAChC;IAEA,IAAI,SAAS,aAAa,EAAE;QAC1B,IAAI,SAAS,cAAc,EAAE;YAC3B,MAAM,IAAI,UAAU;QACtB;QACA,WAAW,cAAc,GAAG;YAAC,SAAS,aAAa;SAAC;QACpD,OAAO,WAAW,aAAa;IACjC;AACF;AAEA,SAAS,uBAAuB,QAAQ,EAAE,MAAM,EAAE,UAAU;IAC1D,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE;IAErC,MAAM,0BAA0B,WAAW,0BAA0B;IACrE,MAAM,8BAA8B,WAAW,+BAA+B;IAE9E,MAAM,MAAM,GAAG,SAAS,qBAAqB,CAAC;IAC9C,MAAM,OAAO,GAAG,SAAS,0BAA0B,CAAC;IAEpD,IAAI,UAAU,CAAC,IAAI,KAAK,aAAa,UAAU,CAAC,KAAK,KAAK,WAAW;QACnE,IAAI,4BAA4B,WAAW;YACzC,UAAU,CAAC,IAAI,GAAG;QACpB;QACA,IAAI,gCAAgC,WAAW;YAC7C,UAAU,CAAC,KAAK,GAAG;QACrB;IACF;AACF;AAEA,MAAM;IACJ,CAAA,QAAS,CAAC;IACV,CAAA,MAAO,CAAC;IACR,CAAA,gBAAiB,CAAC;IAClB,CAAA,2BAA4B,CAAC;IAC7B,YAAY,MAAM,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAE;QAClE,IAAI,CAAC,CAAA,QAAS,GAAG,IAAI;QACrB,IAAI,CAAC,CAAA,MAAO,GAAG;QACf,IAAI,CAAC,CAAA,gBAAiB,GAAG;QAEzB,IAAI,OAAO,SAAS,SAAS,KAAK,YAAY,CAAC,SAAS,SAAS,EAAE;YACjE,MAAM,IAAI,UAAU;QACtB;QAEA,MAAM,aAAa;YACjB,aAAa;gBAAC;aAAqB;YACnC,8BAA8B;YAC9B,mCAAmC;YACnC,gBAAgB;gBAAC;aAAO;YACxB,4BAA4B;YAC5B,GAAI,IAAI,CAAC,KAAK,KACV;gBACE,aAAa;oBAAC;oBAAsB;iBAAW;gBAC/C,8BAA8B;gBAC9B,mCAAmC;gBACnC,gBAAgB;oBAAC;iBAAgB;gBACjC,4CAA4C;gBAC5C,4BAA4B;YAC9B,IACA,SAAS;YACb,GAAI,IAAI,CAAC,KAAK,KACV;gBACE,8BAA8B;gBAC9B,mCAAmC;gBACnC,4BAA4B;YAC9B,IACA,SAAS;YACb,GAAG,QAAQ;QACb;QAEA,IAAI,IAAI,CAAC,IAAI,IAAI;YACf,OAAQ,WAAW,0BAA0B;gBAC3C,KAAK;gBACL,KAAK;oBACH;gBACF,KAAK;oBACH,IAAI,CAAC,MAAM;wBACT,MAAM,IAAI,UAAU;oBACtB;oBACA;gBACF,KAAK;oBACH,MAAM,IAAI,UAAU;gBACtB;oBACE,MAAM,IAAI,UAAU;YACxB;QACF;QAEA,IAAI,IAAI,CAAC,KAAK,IAAI;YAChB,IACE,WAAW,0CAA0C,IACrD,WAAW,wBAAwB,EACnC;gBACA,MAAM,IAAI,UACR;YAEJ;YAEA,IACE,CAAC,WAAW,0CAA0C,IACtD,CAAC,WAAW,wBAAwB,EACpC;gBACA,MAAM,IAAI,UACR;YAEJ;QACF;QAEA,qBAAqB,IAAI,EAAE,UAAU;QAErC,8BAA8B,SAAS,IAAI,CAAC,MAAM,EAAE;QACpD;YAAC;YAAiB;SAAa,CAAC,OAAO,CAAC,CAAC;YACvC,uBAAuB,UAAU,IAAI,CAAC,MAAM,EAAE;YAC9C,8BAA8B,UAAU,IAAI,CAAC,MAAM,EAAE;QACvD;QAEA,OAAO,OAAO,CAAC,YAAY,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC9C,IAAI,CAAC,CAAA,QAAS,CAAC,GAAG,CAAC,KAAK;YACxB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACd,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;oBAC/B;wBACE,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,GAAG,CAAC;oBAC5B;oBACA,YAAY;gBACd;YACF;QACF;QAEA,IAAI,SAAS,WAAW;YACtB,MAAM,WAAW,YAAY,IAAI,CAAC,IAAI,EAAE;YACxC,UAAU,GAAG,CAAC,IAAI,EAAE;QACtB;QAEA,IAAI,WAAW,QAAQ,QAAQ,2BAA2B,EAAE;YAC1D,IAAI,CAAC,CAAA,2BAA4B,GAAG,MAAM,QAAQ,2BAA2B;QAC/E;QAEA,IAAI,CAAC,gBAAgB,GAAG;IAC1B;IAEA,iBAAiB,SAAS,CAAC,CAAC,EAAE;QAC5B,IAAI,CAAC,cAAc,SAAS;YAC1B,MAAM,IAAI,UAAU;QACtB;QACA,0BAA0B,IAAI,CAAC,MAAM,EAAE;QACvC,MAAM,SAAS,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB;QAEzD,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,OAAO,OAAO,CAAC,oBAAoB,IAAI,CAAC,IAAI,EAAE,SAAU;YAClF,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,OAAO,YAAY,CAAC,MAAM,CAAC;gBAC3B,KAAK,MAAM,UAAU,MAAO;oBAC1B,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM;gBACnC;YACF,OAAO;gBACL,OAAO,YAAY,CAAC,GAAG,CAAC,MAAM;YAChC;QACF;QAEA,+BAA+B;QAC/B,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO;IACpC;IAEA,kBAAkB,SAAS,CAAC,CAAC,EAAE;QAC7B,IAAI,CAAC,cAAc,SAAS;YAC1B,MAAM,IAAI,UAAU;QACtB;QACA,MAAM,SAAS,oBAAoB,IAAI,CAAC,IAAI,EAAE;QAC9C,MAAM,aAAa,OAAO,IAAI,CAAC,QAC5B,GAAG,CAAC,CAAC,OAAS,CAAC,2BAA2B,EAAE,KAAK,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAC7E,IAAI,CAAC;QAER,OAAO,CAAC;;;;;4BAKgB,EAAE,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;EAC/D,EAAE,WAAW;;;OAGR,CAAC;IACN;IAEA,cAAc,SAAS,CAAC,CAAC,EAAE;QACzB,0BAA0B,IAAI,CAAC,MAAM,EAAE;QAEvC,MAAM,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,yBAAyB,IAAI,EAAE;QAEtE,MAAM,EAAE,2BAA2B,WAAW,IAAI,aAAa,SAAS,EAAE,GAAG;QAE7E,IAAI;QACJ,CAAC,EAAE,aAAa,EAAE,GAAG,QAAQ,GAAG,MAAM;QACtC,IAAI,yBAAyB,UAAU;YACrC,IAAI,CAAC,cAAc,QAAQ,EAAE;gBAC3B,MAAM,IAAI,UAAU;YACtB;YACA,gBAAgB,cAAc,QAAQ;QACxC;QAEA,MAAM,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB;QACzD,MAAM,QAAQ,SACZ,gBAAgB,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAChD,QACA;YACE;YACA,WAAW,IAAI,CAAC,SAAS;QAC3B,GACA;YAAE;QAAc;QAGlB,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACzC,IAAI,UAAU,QAAQ,UAAU,WAAW;gBACzC,OAAO,KAAK,CAAC,IAAI;YACnB;QACF;QAEA,OAAO,MAAM,GAAG;QAChB,OAAO,KAAK,GAAG;QAEf,OAAO,IAAI,MAAM,CAAC;IACpB;IAEA,eAAe,KAAK,EAAE;QACpB,MAAM,oBACJ,iBAAiB,QAAQ,eAAe,IAAK,SAAS,MAAM,MAAM,IAAI,MAAM,GAAG;QACjF,MAAM,WAAW,OAAO,UAAU;QAElC,IAAI,CAAC,YAAY,CAAC,mBAAmB;YACnC,MAAM,IAAI,UACR;QAEJ;QACA,IAAI,mBAAmB;YACrB,OAAQ,MAAM,MAAM;gBAClB,KAAK;oBACH,OAAO,OAAO,gBAAgB,MAAM,GAAG;gBACzC,KAAK;oBACH,IAAI,MAAM,IAAI,KAAK,WAAW;wBAC5B,MAAM,IAAI,UACR;oBAEJ;oBACA,OAAQ,OAAO,MAAM,IAAI;wBACvB,KAAK;wBACL,KAAK;4BACH,IAAI,OAAO,QAAQ,CAAC,MAAM,IAAI,GAAG;gCAC/B,OAAO,OAAO,YAAY,KAAK,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC;4BACtD;4BACA,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;gCAClC,OAAO,OAAO,YAAY,KAAK,CAAC,MAAM,IAAI;4BAC5C;4BAEA,OAAO,OAAO,MAAM,IAAI;wBAC1B;4BACE,MAAM,IAAI,UAAU;oBACxB;gBACF;oBACE,MAAM,IAAI,UAAU;YACxB;QACF,OAAO;YACL,OAAO,OAAO,gBAAgB;QAChC;IACF;IAEA,MAAM,SACJ,WAAW,EACX,UAAU,EACV,SAAS,CAAC,CAAC,EACX,EAAE,YAAY,EAAE,sBAAsB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EACnD;QACA,IAAI,SAAS,OAAO;QAEpB,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC,cAAc,UAAU,GAAG;YAC9C,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;gBACA;YACF;QACF,OAAO,IAAI,cAAc,YAAY;YACnC,MAAM,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,QAAQ;YACxD,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;QACnC;QAEA,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,OAAO,OAAO,EAAE;YAC3C,OAAO,OAAO,GAAG,IAAI,CAAC,eAAe;QACvC;QAEA,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK,EAAE;YACjC,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,CAAC,OAAO,KAAK,IAAI,OAAO,KAAK,EAAE;YACjC,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;gBACA;YACF;QACF;QAEA,IAAI,OAAO,KAAK,KAAK,OAAO,KAAK,EAAE;YACjC,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBAAC;oBAAwC,OAAO,KAAK;oBAAE,OAAO,KAAK;iBAAC;gBAC5E;gBACA;YACF;QACF;QAEA,IAAI,SAAS,QAAQ;YACnB,0BAA0B,IAAI,CAAC,MAAM,EAAE;YACvC,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBACrC,MAAM,IAAI,QAAQ;oBAChB,QAAQ;wBAAC;wBAAsC,IAAI,CAAC,MAAM,CAAC,MAAM;wBAAE,OAAO,GAAG;qBAAC;oBAC9E;gBACF;YACF;QACF,OAAO,IACL,IAAI,CAAC,MAAM,CAAC,8CAA8C,IAC1D,CAAC,CAAC,cAAc,MAAM,KACtB,CAAC,CAAC,cAAc,UAAU,GAC1B;YACA,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;YACF;QACF;QAEA,IAAI,OAAO,KAAK,EAAE;YAChB,MAAM,IAAI,QAAQ;QACpB;QAEA,MAAM,gCAAgC;YACpC,MAAM;gBAAC;aAAO;YACd,UAAU;gBAAC;aAAW;YACtB,OAAO;gBAAC;gBAAgB;aAAa;QACvC;QAEA,IAAI,OAAO,aAAa,EAAE;YACxB,KAAK,MAAM,QAAQ,OAAO,aAAa,CAAC,KAAK,CAAC,KAAM;gBAClD,IAAI,SAAS,QAAQ;oBACnB,IAAI,OAAO,IAAI,IAAI,OAAO,QAAQ,IAAI,OAAO,YAAY,EAAE;wBACzD,MAAM,IAAI,QAAQ;4BAChB,SAAS;4BACT;4BACA;wBACF;oBACF;gBACF,OAAO;oBACL,KAAK,MAAM,SAAS,6BAA6B,CAAC,KAAK,CAAE;wBACvD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;4BAClB,MAAM,IAAI,QAAQ;gCAChB,SAAS,GAAG,MAAM,sBAAsB,CAAC;gCACzC;gCACA;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,IAAI,OAAO,QAAQ,EAAE;YACnB,MAAM,WAAW,IAAI,SAAS;YAC9B,MAAM,IAAI,CAAC,cAAc,CAAC;YAC1B,MAAM,IAAI,CAAC,eAAe,CACxB,UACA,OAAO,KAAK,EACZ,iBACA,OAAO,OAAO,EACd,OAAO,KAAK;YAGd,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,OAAO;YACT;QACF;QAEA,IAAI,OAAO,IAAI,EAAE;YACf,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAC/B;gBACE,GAAG,YAAY;gBACf,YAAY;gBACZ,MAAM,OAAO,IAAI;gBACjB,cAAc;gBACd,eAAe,OAAO,aAAa;YACrC,GACA;gBAAE;gBAAwB;YAAK;YAGjC,MAAM,IAAI,CAAC,cAAc,CAAC;YAC1B,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,OAAO,KAAK,EAAE,SAAS,OAAO,OAAO;YAE1E,IAAI,OAAO,aAAa,EAAE;gBACxB,SAAS,aAAa,GAAG,OAAO,aAAa;YAC/C;YAEA,OAAO;QACT;QAEA,OAAO,IAAI,SAAS;IACtB;IAEA,MAAM,cACJ,WAAW,EACX,UAAU,EACV,SAAS,CAAC,CAAC,EACX,EAAE,YAAY,EAAE,sBAAsB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EACnD;QACA,IAAI,SAAS,OAAO;QAEpB,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC,cAAc,UAAU,GAAG;YAC9C,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;gBACA;YACF;QACF,OAAO,IAAI,cAAc,YAAY;YACnC,MAAM,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,QAAQ;YACxD,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;QACnC;QAEA,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK,EAAE;YACjC,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,CAAC,OAAO,KAAK,IAAI,OAAO,KAAK,EAAE;YACjC,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;gBACA;YACF;QACF;QAEA,IAAI,OAAO,KAAK,KAAK,OAAO,KAAK,EAAE;YACjC,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBAAC;oBAAwC,OAAO,KAAK;oBAAE,OAAO,KAAK;iBAAC;gBAC5E;gBACA;YACF;QACF;QAEA,IAAI,SAAS,QAAQ;YACnB,0BAA0B,IAAI,CAAC,MAAM,EAAE;YACvC,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBACrC,MAAM,IAAI,QAAQ;oBAChB,QAAQ;wBAAC;wBAAsC,IAAI,CAAC,MAAM,CAAC,MAAM;wBAAE,OAAO,GAAG;qBAAC;oBAC9E;gBACF;YACF;QACF,OAAO,IACL,IAAI,CAAC,MAAM,CAAC,8CAA8C,IAC1D,CAAC,CAAC,cAAc,MAAM,KACtB,CAAC,CAAC,cAAc,UAAU,GAC1B;YACA,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;YACF;QACF;QAEA,IAAI,OAAO,KAAK,EAAE;YAChB,MAAM,IAAI,QAAQ;QACpB;QAEA,IAAI,OAAO,OAAO,QAAQ,KAAK,YAAY,OAAO,QAAQ,CAAC,MAAM,EAAE;YACjE,MAAM,IAAI,QAAQ;gBAChB,SACE;gBACF;YACF;QACF;QACA,OAAO,OAAO,QAAQ;QAEtB,MAAM,gCAAgC;YACpC,MAAM;gBAAC;aAAO;YACd,OAAO;gBAAC;gBAAgB;aAAa;QACvC;QAEA,IAAI,OAAO,aAAa,EAAE;YACxB,KAAK,MAAM,QAAQ,OAAO,aAAa,CAAC,KAAK,CAAC,KAAM;gBAClD,IAAI,SAAS,QAAQ;oBACnB,IAAI,OAAO,IAAI,IAAI,OAAO,QAAQ,IAAI,OAAO,YAAY,EAAE;wBACzD,MAAM,IAAI,QAAQ;4BAChB,SAAS;4BACT;4BACA;wBACF;oBACF;gBACF;gBAEA,IAAI,6BAA6B,CAAC,KAAK,EAAE;oBACvC,KAAK,MAAM,SAAS,6BAA6B,CAAC,KAAK,CAAE;wBACvD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;4BAClB,MAAM,IAAI,QAAQ;gCAChB,SAAS,GAAG,MAAM,sBAAsB,CAAC;gCACzC;gCACA;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,IAAI,OAAO,IAAI,EAAE;YACf,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAC/B;gBACE,GAAG,YAAY;gBACf,YAAY;gBACZ,MAAM,OAAO,IAAI;gBACjB,cAAc;gBACd,eAAe,OAAO,aAAa;YACrC,GACA;gBAAE;gBAAwB;YAAK;YAGjC,IAAI,OAAO,SAAS,QAAQ,KAAK,YAAY,SAAS,QAAQ,CAAC,MAAM,EAAE;gBACrE,MAAM,IAAI,QAAQ;oBAChB,SACE;oBACF;gBACF;YACF;YACA,OAAO,SAAS,QAAQ;YAExB,OAAO;QACT;QAEA,OAAO,IAAI,SAAS;IACtB;IAEA,MAAM,eAAe,KAAK,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,+BAA+B,EAAE;YACzC,OAAO;QACT;QAEA,IAAI,UAAU;QAEd,IAAI,mBAAmB,UAAU;YAC/B,IAAI,CAAC,QAAQ,QAAQ,EAAE;gBACrB,MAAM,IAAI,UAAU;YACtB;YACA,UAAU,QAAQ,QAAQ;QAC5B;QAEA,MAAM,cAAc,IAAI,CAAC,+BAA+B;QACxD,MAAM,cAAc,IAAI,CAAC,+BAA+B;QAExD,MAAM,SAAS,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,aAAa;QAE3D,IAAI,iBAAiB,UAAU;YAC7B,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB,IAAI,EAAE;QAC9B,MAAM,cAAc,IAAI,CAAC,4BAA4B;QAErD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,aAAa,EAAE;IAC/C;IAEA,MAAM,YAAY,QAAQ,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,oCAAoC,EAAE;YAC9C,OAAO;QACT;QAEA,MAAM,cAAc,IAAI,CAAC,oCAAoC;QAC7D,MAAM,cAAc,IAAI,CAAC,oCAAoC;QAE7D,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,aAAa;IAChD;IAEA,MAAM,mBAAmB,IAAI,EAAE;QAC7B,IAAI,CAAC,IAAI,CAAC,+BAA+B,EAAE;YACzC,OAAO;QACT;QAEA,MAAM,cAAc,IAAI,CAAC,+BAA+B;QACxD,MAAM,cAAc,IAAI,CAAC,+BAA+B;QAExD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,aAAa;IAC5C;IAEA,MAAM,WAAW,GAAG,EAAE,WAAW,EAAE,cAAc,eAAe,EAAE;QAChE,MAAM,SAAS,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;QAE5D,IAAI,OAAO,GAAG,KAAK,aAAa;YAC9B,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBAAC;oBAAqD;oBAAa,OAAO,GAAG;iBAAC;gBACtF,KAAK;YACP;QACF;QAEA,IAAI,OAAO,GAAG,KAAK,aAAa;YAC9B,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBAAC;oBAAqD;oBAAa,OAAO,GAAG;iBAAC;gBACtF,KAAK;YACP;QACF;QAEA,MAAM,eAAe,CAAC,SAAW,IAAI,cAAc,MAAM,CAAC,OAAO,SAAS;QAC1E,IAAI;QACJ,IAAI,YAAY,KAAK,CAAC,kBAAkB;YACtC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,IAAI;YAEzC,MAAM,kBAAkB,KAAK,qBAAqB,CAAC;YAEnD,KAAK,MAAM,OAAO,SAAS,GAAG,CAAC;gBAC7B,GAAG,eAAe;gBAClB,KAAK;YACP,GAAI;gBACF,YAAY,MAAM,KACf,cAAc,CAAC,KAAK,MAAM,IAAI,SAAS,CAAC,gBAAgB,GAAG,GAC3D,IAAI,CAAC,cAAc,KAAO;gBAC7B,IAAI,WAAW;YACjB;QACF,OAAO;YACL,YAAY,MAAM,KACf,cAAc,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,gBAAgB,QAAQ,cAAc,cAC5E,IAAI,CAAC,cAAc,KAAO;QAC/B;QAEA,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT,KAAK;YACP;QACF;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE;QAChE,IAAI,UAAU;QAEd,MAAM,cAAc,IAAI,CAAC,4BAA4B;QAErD,MAAM,aAAa,mBAAmB;QAEtC,IAAI,YAAY;YACd,IAAI,CAAC,QAAQ,QAAQ,EAAE;gBACrB,MAAM,IAAI,UAAU;YACtB;YACA,UAAU,QAAQ,QAAQ;QAC5B;QAEA,UAAU,OAAO;QAEjB,MAAM,YAAY;QAClB,MAAM,EAAE,WAAW,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS;QAE5E,IAAI,OAAO,WAAW,YAAa,WAAW,mBAAmB,IAAI,CAAC,iBAAiB,EAAG;YACxF,IAAI,CAAC,QAAQ,SAAS,EAAE;gBACtB,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT,KAAK;gBACP;YACF;YACA,IAAI,OAAO,QAAQ,SAAS,KAAK,UAAU;gBACzC,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT,KAAK;gBACP;YACF;QACF;QAEA,IACE,OAAO,WAAW,YAClB,QAAQ,SAAS,GAAG,SAAS,YAAY,IAAI,CAAC,gBAAgB,EAC9D;YACA,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBACN;oBACA;oBACA,QAAQ,SAAS;oBACjB,YAAY,IAAI,CAAC,gBAAgB;iBAClC;gBACD,KAAK;gBACL,WAAW,IAAI,CAAC,gBAAgB;gBAChC,WAAW,QAAQ,SAAS;gBAC5B,KAAK;YACP;QACF;QAEA,IACE,UAAU,kBACV,CAAC,QAAQ,KAAK,IAAI,UAAU,SAAS,KACrC,QAAQ,KAAK,KAAK,OAClB;YACA,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBAAC;oBAAwC;oBAAO,QAAQ,KAAK;iBAAC;gBACtE,KAAK;YACP;QACF;QAEA,IAAI,eAAe,iBAAiB;YAClC,IAAI,CAAC,QAAQ,OAAO,IAAI,SAAS,YAAY,EAAE;gBAC7C,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT,KAAK;gBACP;YACF;YAEA,IAAI,CAAC,QAAQ,MAAM,IAAI,SAAS,IAAI,EAAE;gBACpC,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT,KAAK;gBACP;YACF;YAEA,IAAI,IAAI,CAAC,KAAK,IAAI;gBAChB,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,KAAK,GAAG;oBAChD,MAAM,IAAI,QAAQ;wBAChB,SAAS;wBACT,KAAK;oBACP;gBACF;YACF;YAEA,IAAI,QAAQ,MAAM,EAAE;gBAClB,IAAI,CAAC,OAAO;oBACV,MAAM,IAAI,UAAU;gBACtB;gBAEA,IAAI;oBACF,UAAU,QAAQ,CAChB;wBAAE,OAAO;wBAAU,QAAQ;oBAAQ,GACnC,QAAQ,MAAM,EACd,OACA,OAAO,GAAG,EACV,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG;gBAE1B,EAAE,OAAO,KAAK;oBACZ,MAAM,IAAI,QAAQ;wBAAE,SAAS,IAAI,OAAO;wBAAE,KAAK;oBAAQ;gBACzD;YACF;QACF;QAEA,IAAI,IAAI,CAAC,IAAI,MAAM,QAAQ,GAAG,GAAG,YAAY,MAAM;YACjD,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBAAC;oBAAkD;oBAAW,QAAQ,GAAG;iBAAC;gBAClF,KAAK;gBACL,WAAW,IAAI,CAAC,gBAAgB;gBAChC,KAAK,QAAQ,GAAG;gBAChB,KAAK;YACP;QACF;QAEA,IAAI,SAAS,YAAY,IAAI,QAAQ,OAAO,KAAK,WAAW;YAC1D,IAAI;gBACF,UAAU,QAAQ,CAChB;oBAAE,OAAO;oBAAW,QAAQ;gBAAe,GAC3C,QAAQ,OAAO,EACf,SAAS,YAAY,EACrB,OAAO,GAAG,EACV,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG;YAE1B,EAAE,OAAO,KAAK;gBACZ,MAAM,IAAI,QAAQ;oBAAE,SAAS,IAAI,OAAO;oBAAE,KAAK;gBAAQ;YACzD;QACF;QAEA,IAAI,SAAS,IAAI,IAAI,QAAQ,MAAM,KAAK,WAAW;YACjD,IAAI;gBACF,UAAU,QAAQ,CAChB;oBAAE,OAAO;oBAAU,QAAQ;gBAAO,GAClC,QAAQ,MAAM,EACd,SAAS,IAAI,EACb,OAAO,GAAG,EACV,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG;YAE1B,EAAE,OAAO,KAAK;gBACZ,MAAM,IAAI,QAAQ;oBAAE,SAAS,IAAI,OAAO;oBAAE,KAAK;gBAAQ;YACzD;QACF;QAEA,OAAO;IACT;IAEA,MAAM,YAAY,GAAG,EAAE,WAAW,EAAE,WAAW;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM,EAAE;QAClF,MAAM,eAAe,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK;QAC5C,MAAM,YAAY;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;YACF,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,KAAK;gBAAE,UAAU;YAAK,EAAE;QAC3D,EAAE,OAAO,KAAK;YACZ,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBAAC;oBAAiC,IAAI,IAAI;oBAAE,IAAI,OAAO;iBAAC;gBAChE;YACF;QACF;QAEA,IAAI,OAAO,GAAG,KAAK,aAAa;YAC9B,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBAAC;oBAAqD;oBAAa,OAAO,GAAG;iBAAC;gBACtF;YACF;QACF;QAEA,IAAI,cAAc;YAChB,WAAW;mBAAI;gBAAU;aAAU;QACrC;QAEA,SAAS,OAAO,CAAC,eAAe,IAAI,CAAC,WAAW,SAAS;QAEzD,IAAI,QAAQ,GAAG,KAAK,WAAW;YAC7B,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,MAAM;YAEpC,IAAI,IAAI,CAAC,CAAA,gBAAiB,EAAE;gBAC1B,cAAc,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,QAAQ,GAAG;YACpE;YAEA,IAAI,QAAQ,GAAG,KAAK,aAAa;gBAC/B,MAAM,IAAI,QAAQ;oBAChB,QAAQ;wBAAC;wBAA8C;wBAAa,QAAQ,GAAG;qBAAC;oBAChF;gBACF;YACF;QACF;QAEA,IAAI,QAAQ,GAAG,KAAK,WAAW;YAC7B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;gBACnC,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT;gBACF;YACF;QACF;QAEA,IAAI,QAAQ,GAAG,KAAK,WAAW;YAC7B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;gBACnC,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT;gBACF;YACF;YACA,IAAI,QAAQ,GAAG,GAAG,YAAY,IAAI,CAAC,gBAAgB,EAAE;gBACnD,MAAM,IAAI,QAAQ;oBAChB,QAAQ;wBACN;wBACA,YAAY,IAAI,CAAC,gBAAgB;wBACjC,QAAQ,GAAG;qBACZ;oBACD,KAAK;oBACL,WAAW,IAAI,CAAC,gBAAgB;oBAChC,KAAK,QAAQ,GAAG;oBAChB;gBACF;YACF;QACF;QAEA,IAAI,QAAQ,GAAG,KAAK,WAAW;YAC7B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;gBACnC,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT;gBACF;YACF;YACA,IAAI,YAAY,IAAI,CAAC,gBAAgB,IAAI,QAAQ,GAAG,EAAE;gBACpD,MAAM,IAAI,QAAQ;oBAChB,QAAQ;wBAAC;wBAA+B,YAAY,IAAI,CAAC,gBAAgB;wBAAE,QAAQ,GAAG;qBAAC;oBACvF,KAAK;oBACL,WAAW,IAAI,CAAC,gBAAgB;oBAChC,KAAK,QAAQ,GAAG;oBAChB;gBACF;YACF;QACF;QAEA,IAAI,QAAQ,GAAG,KAAK,WAAW;YAC7B,IAAI,MAAM,OAAO,CAAC,QAAQ,GAAG,GAAG;gBAC9B,IAAI,QAAQ,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,GAAG,EAAE;oBAC1C,MAAM,IAAI,QAAQ;wBAChB,SAAS;wBACT;oBACF;gBACF;gBAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,GAAG;oBACzC,MAAM,IAAI,QAAQ;wBAChB,QAAQ;4BACN;4BACA,IAAI,CAAC,SAAS;4BACd,QAAQ,GAAG;yBACZ;wBACD;oBACF;gBACF;YACF,OAAO,IAAI,QAAQ,GAAG,KAAK,IAAI,CAAC,SAAS,EAAE;gBACzC,MAAM,IAAI,QAAQ;oBAChB,QAAQ;wBAAC;wBAAsC,IAAI,CAAC,SAAS;wBAAE,QAAQ,GAAG;qBAAC;oBAC3E;gBACF;YACF;QACF;QAEA,IAAI,QAAQ,GAAG,KAAK,WAAW;YAC7B,IAAI,8BAA8B,IAAI,CAAC,CAAA,2BAA4B;YAEnE,IAAI,OAAO,gCAAgC,UAAU;gBACnD,8BAA8B;oBAAC,IAAI,CAAC,SAAS;oBAAE;iBAA4B;YAC7E,OAAO,IAAI,MAAM,OAAO,CAAC,8BAA8B;gBACrD,8BAA8B;oBAAC,IAAI,CAAC,SAAS;uBAAK;iBAA4B;YAChF,OAAO;gBACL,8BAA8B;oBAAC,IAAI,CAAC,SAAS;iBAAC;YAChD;YAEA,IAAI,CAAC,4BAA4B,QAAQ,CAAC,QAAQ,GAAG,GAAG;gBACtD,MAAM,IAAI,QAAQ;oBAChB,QAAQ;wBAAC;wBAAyB,QAAQ,GAAG;qBAAC;oBAC9C;gBACF;YACF;QACF;QAEA,IAAI;QAEJ,IAAI,cAAc;YAChB,IAAI;gBACF,OAAO,cAAc,QAAQ,OAAO;gBACpC,MAAM,MAAM,MAAM,KAAK,SAAS,CAAC,QAAQ,OAAO,EAAE,OAAO,GAAG;gBAC5D,OAAO,KAAK,CAAC,IAAI,IAAI,EAAE;gBACvB,OAAO;oBACL;wBACE;4BACE,OAAO;wBACT;oBACF;iBACD;YACH,EAAE,OAAO,KAAK;gBACZ,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT;gBACF;YACF;YACA,IAAI,AAAC,MAAM,KAAK,sBAAsB,CAAC,QAAQ,OAAO,MAAO,QAAQ,GAAG,EAAE;gBACxE,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT;gBACF;YACF;QACF,OAAO,IAAI,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO;YACtC,OAAO;gBAAC,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG;aAAE;QACxC,OAAO,IAAI,OAAO,GAAG,KAAK,QAAQ;YAChC,OAAO,MAAM,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAAE,GAAG,MAAM;gBAAE,KAAK;YAAM;QACvE;QAEA,IAAI,CAAC,QAAQ,OAAO,GAAG,KAAK,QAAQ;YAClC,OAAO;gBAAE,WAAW;gBAAQ;YAAQ;QACtC;QAEA,KAAK,MAAM,OAAO,KAAM;YACtB,MAAM,WAAW,MAAM,KACpB,aAAa,CAAC,KAAK,eAAe,aAAa,MAAM,MAAM,IAAI,SAAS,CAAC,OAAO,GAAG,GACnF,KAAK,CAAC,KAAO;YAChB,IAAI,UAAU;gBACZ,OAAO;oBACL;oBACA,WAAW,SAAS,eAAe;oBACnC;gBACF;YACF;QACF;QAEA,MAAM,IAAI,QAAQ;YAChB,SAAS;YACT;QACF;IACF;IAEA,MAAM,QAAQ,YAAY,EAAE,EAAE,YAAY,EAAE,sBAAsB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QAC/E,IAAI,QAAQ;QAEZ,IAAI,iBAAiB,UAAU;YAC7B,IAAI,CAAC,MAAM,aAAa,EAAE;gBACxB,MAAM,IAAI,UAAU;YACtB;YACA,QAAQ,MAAM,aAAa;QAC7B;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAC/B;YACE,GAAG,YAAY;YACf,YAAY;YACZ,eAAe,OAAO;QACxB,GACA;YAAE;YAAwB;QAAK;QAGjC,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,IAAI,CAAC,cAAc,CAAC;YAC1B,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,gBAAgB,SAAS;YAE9D,IAAI,wBAAwB,YAAY,aAAa,QAAQ,EAAE;gBAC7D,MAAM,cAAc,aAAa,MAAM,GAAG,GAAG;gBAC7C,MAAM,YAAY,SAAS,MAAM,GAAG,GAAG;gBACvC,IAAI,cAAc,aAAa;oBAC7B,MAAM,IAAI,QAAQ;wBAChB,QAAQ;4BAAC;4BAAsC;4BAAa;yBAAU;wBACtE,KAAK,SAAS,QAAQ;oBACxB;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,gBACJ,WAAW,EACX,WAAW,EACX,EACE,MAAM,EACN,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,YAAY,OACR,SACA,uBAAuB,WACvB,YAAY,UAAU,GACtB,QAAQ,EACb,GAAG,CAAC,CAAC,EACN,KAAK,EACL;QACA,IAAI,uBAAuB,UAAU;YACnC,IAAI,CAAC,YAAY,YAAY,EAAE;gBAC7B,MAAM,IAAI,UAAU;YACtB;YACA,cAAc,YAAY,YAAY;QACxC;QAEA,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,UAAU;QACtB,OAAO,IAAI,OAAO,gBAAgB,UAAU;YAC1C,MAAM,IAAI,UAAU;QACtB;QAEA,MAAM,cAAc;YAClB,SAAS;gBACP,eAAe,yBAAyB,aAAa;gBACrD,GAAG,OAAO;YACZ;YACA;QACF;QAEA,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,0CAA0C;QAE9D,MAAM,WAAW,MAAM,QAAQ,IAAI,CACjC,IAAI,EACJ;YACE,GAAG,WAAW;YACd,cAAc;YACd;YACA,KAAK;QACP,GACA;YAAE;YAAa;YAAM;QAAK;QAG5B,MAAM,kBAAkB,SAAS,OAAO,CAAC,mBAAmB;QAC5D,IACE,UAAU,gBACV,mBACA,gBAAgB,WAAW,GAAG,UAAU,CAAC,YACzC,qBAAqB,iBAAiB,KAAK,KAAK,kBAChD;YACA,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,aAAa;gBACpD;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,SAAS,WAAW,EAAE,EAAE,SAAS,KAAK,EAAE,MAAM,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QAC5F,0BAA0B,IAAI,CAAC,MAAM,EAAE;QACvC,MAAM,UAAU;YACd;YACA,QAAQ,OAAO,QAAQ,WAAW;YAClC;QACF;QAEA,IAAI,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,QAAQ;YACzD,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,QAAQ,UAAU,QAAQ,MAAM,KAAK,QAAQ;YAC/C,MAAM,IAAI,UAAU;QACtB;QAEA,MAAM,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,+BAA+B;QAExF,IAAI,KAAK;YACP,QAAQ,OAAO,GAAG;gBAAE,QAAQ;YAAkB;QAChD,OAAO;YACL,QAAQ,OAAO,GAAG;gBAAE,QAAQ;YAAmB;QACjD;QACA,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,0CAA0C;QAE9D,IAAI;QACJ,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;YAC7C,YAAY,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,iBAAiB;QACjE;QAEA,YAAY,IAAI,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,iBAAiB;QAE9D,IAAI,QAAQ,QAAQ;YAClB,QAAQ,OAAO,CAAC,aAAa,GAAG;YAChC,QAAQ,OAAO,CAAC,eAAe,GAAG;YAClC,QAAQ,IAAI,GAAG,IAAI;YACnB,QAAQ,IAAI,CAAC,MAAM,CACjB,gBACA,uBAAuB,WAAW,YAAY,YAAY,GAAG;QAEjE;QAEA,8EAA8E;QAC9E,IAAI,QAAQ;YACV,IAAI,QAAQ,MAAM,KAAK,OAAO;gBAC5B,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;oBAC1C,UAAU,YAAY,CAAC,MAAM,CAAC,KAAK;gBACrC;YACF,OAAO,IAAI,QAAQ,IAAI,EAAE;gBACvB,mBAAmB;gBACnB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;oBAC1C,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;gBAC3B;YACF,OAAO;gBACL,qBAAqB;gBACrB,QAAQ,IAAI,GAAG,IAAI;gBACnB,QAAQ,OAAO,CAAC,eAAe,GAAG;gBAClC,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;oBAC1C,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;gBAC3B;YACF;QACF;QAEA,IAAI,QAAQ,IAAI,EAAE;YAChB,QAAQ,IAAI,GAAG,QAAQ,IAAI,CAAC,QAAQ;QACtC;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,aAAa;QAEpE,IAAI,SAAS,gBAAgB,UAAU;YAAE,QAAQ;QAAK;QAEtD,IAAI,KAAK;YACP,IAAI,CAAC,oBAAoB,IAAI,CAAC,SAAS,OAAO,CAAC,eAAe,GAAG;gBAC/D,MAAM,IAAI,QAAQ;oBAChB,SAAS;oBACT;gBACF;YACF;YAEA,MAAM,OAAO,SAAS,IAAI,CAAC,QAAQ;YACnC,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACtC,IAAI;oBACF,SAAS,KAAK,KAAK,CAAC;oBACpB,OAAO,cAAc;gBACvB,EAAE,OAAO,KAAK;oBACZ,MAAM,IAAI,QAAQ;wBAChB,SAAS;wBACT,KAAK;oBACP;gBACF;YACF,OAAO;gBACL,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS;YACjE;QACF,OAAO;YACL,IAAI;gBACF,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACnC,EAAE,OAAO,KAAK;gBACZ,OAAO,cAAc,CAAC,KAAK,YAAY;oBAAE,OAAO;gBAAS;gBACzD,MAAM;YACR;QACF;QAEA,IAAI,uBAAuB,YAAY,YAAY,QAAQ,EAAE;YAC3D,MAAM,cAAc,YAAY,MAAM,GAAG,GAAG;YAC5C,IAAI,OAAO,GAAG,KAAK,aAAa;gBAC9B,MAAM,IAAI,QAAQ;oBAChB,QAAQ;wBAAC;wBAA+C;wBAAa,OAAO,GAAG;qBAAC;oBAChF,MAAM;oBACN,KAAK,YAAY,QAAQ;gBAC3B;YACF;QACF;QAEA,OAAO;IACT;IAEA,iBAAiB,GAAG,EAAE;QACpB,MAAM,OAAO,OAAO,MAAM,WAAW,OAAO,MAAM,WAAW,OAAO,MAAM,WAAW;QACrF,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,OACJ,UAAU,CAAC,MACX,MAAM,CAAC,IAAI,CAAC,aAAa,EACzB,MAAM,GACN,KAAK,CAAC,GAAG,MAAM;IACpB;IAEA,aAAa,GAAG,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,uBAAuB,IAAI,CAAC,MAAM;YACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,OAAO,EAAE,EAAE;QACnD;QAEA,IAAI,kCAAkC,IAAI,CAAC,MAAM;YAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,OAAO,EAAE,IAAI,OAAO,EAAE,EAAE;QAChE;QAEA,OAAO,IAAI,cAAc,MAAM,CAAC,IAAI,CAAC,aAAa;IACpD;IAEA,MAAM,MAAM,IAAI,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE;QAC9D,0BAA0B,IAAI,CAAC,MAAM,EAAE;QACvC,MAAM,WAAW,MAAM,kBAAkB,IAAI,CAC3C,IAAI,EACJ,SACA;YACE,MAAM;YACN,cAAc;QAChB,GACA;YAAE;YAAwB;QAAK;QAEjC,IAAI;QACJ,IAAI;YACF,eAAe,gBAAgB;QACjC,EAAE,OAAO,KAAK;YACZ,IAAI,UAAU,gBAAgB,eAAe,WAAW,IAAI,KAAK,KAAK,kBAAkB;gBACtF,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;oBAAE;oBAAwB;gBAAK,GAAG;YAC5D;YACA,MAAM;QACR;QAEA,OAAO,IAAI,SAAS;IACtB;IAEA,MAAM,oBAAoB,SAAS,CAAC,CAAC,EAAE,EAAE,YAAY,EAAE,sBAAsB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QAC1F,0BAA0B,IAAI,CAAC,MAAM,EAAE;QACvC,0BAA0B,IAAI,CAAC,MAAM,EAAE;QAEvC,MAAM,OAAO,oBAAoB,IAAI,CAAC,IAAI,EAAE;YAC1C,WAAW,IAAI,CAAC,SAAS;YACzB,cAAc;YACd,eAAe;YACf,GAAG,MAAM;QACX;QAEA,MAAM,WAAW,MAAM,kBAAkB,IAAI,CAC3C,IAAI,EACJ,wBACA;YACE,cAAc;YACd,MAAM;QACR,GACA;YAAE;YAAwB,oBAAoB;QAAQ;QAExD,MAAM,eAAe,gBAAgB;QAErC,OAAO,IAAI,iBAAiB;YAC1B,QAAQ,IAAI;YACZ;YACA;YACA,UAAU;YACV,QAAQ,OAAO,OAAO;YACtB;QACF;IACF;IAEA,MAAM,OAAO,KAAK,EAAE,IAAI,EAAE,EAAE,UAAU,EAAE,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;QACrE,0BAA0B,IAAI,CAAC,MAAM,EAAE;QACvC,IAAI,SAAS,aAAa,OAAO,SAAS,UAAU;YAClD,MAAM,IAAI,UAAU;QACtB;QAEA,MAAM,OAAO;YAAE,GAAG,UAAU;YAAE;QAAM;QAEpC,IAAI,MAAM;YACR,KAAK,eAAe,GAAG;QACzB;QAEA,MAAM,WAAW,MAAM,kBAAkB,IAAI,CAC3C,IAAI,EACJ,cACA;YACE;QACF,GACA;YAAE;QAAuB;QAE3B,gBAAgB,UAAU;YAAE,MAAM;QAAM;IAC1C;IAEA,MAAM,WAAW,KAAK,EAAE,IAAI,EAAE,EAAE,cAAc,EAAE,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;QAC7E,0BAA0B,IAAI,CAAC,MAAM,EAAE;QACvC,IAAI,SAAS,aAAa,OAAO,SAAS,UAAU;YAClD,MAAM,IAAI,UAAU;QACtB;QAEA,MAAM,OAAO;YAAE,GAAG,cAAc;YAAE;QAAM;QACxC,IAAI,MAAM;YACR,KAAK,eAAe,GAAG;QACzB;QAEA,MAAM,WAAW,MAAM,kBAAkB,IAAI,CAC3C,IAAI,EACJ,iBACA;YAAE;YAAM,cAAc;QAAO,GAC7B;YAAE;QAAuB;QAG3B,MAAM,eAAe,gBAAgB;QAErC,OAAO;IACT;IAEA,aAAa,SAAS,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE;QAC5C,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,GAAG,eAAe,GAAG;QAEvD,0BAA0B,IAAI,CAAC,MAAM,EAAE;QAEvC,IAAI,SAAS,aAAa,CAAC,CAAC,SAAS,IAAI,IAAI,SAAS,QAAQ,GAAG;YAC/D,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,IAAI,EAAE;YAC9C,SAAS,IAAI,GAAG,SAAS,MAAM;QACjC;QAEA,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC,IAAI,EAAE;YACxC,SAAS;gBACP,QAAQ;gBACR,GAAI,qBACA;oBACE,eAAe,yBAAyB;gBAC1C,IACA,SAAS;YACf;YACA,cAAc;YACd,MAAM;YACN,KAAK,IAAI,CAAC,MAAM,CAAC,qBAAqB;YACtC,QAAQ;QACV;QACA,MAAM,eAAe,gBAAgB,UAAU;YAAE,YAAY;YAAK,QAAQ;QAAK;QAE/E,OAAO,IAAI,IAAI,CAAC,cAAc,MAAM;IACtC;IAEA,IAAI,WAAW;QACb,OAAO,MAAM,OAAO,WAAW,CAAC,IAAI,CAAC,CAAA,QAAS,CAAC,OAAO;IACxD;IAEA,aAAa,QAAQ,qBAAqB,EAAE,uBAAuB,EAAE,IAAI,EAAE,aAAa,EAAE;QACxF,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC,IAAI,EAAE;YACxC,QAAQ;YACR,KAAK;YACL,cAAc;YACd,SAAS;gBACP,eAAe,yBAAyB;gBACxC,QAAQ;YACV;QACF;QACA,MAAM,eAAe,gBAAgB,UAAU;YAAE,QAAQ;QAAK;QAE9D,OAAO,IAAI,IAAI,CAAC,cAAc,MAAM;IACtC;IAEA,MAAM,cACJ,gBAAgB,CAAC,CAAC,EAClB,EACE,MAAM,mBAAmB,IAAI,CAAC,0BAA0B,IAAI,MAAM,EAClE,SAAS,EACP,KAAK,iBAAiB,IAAI,CAAC,6BAA6B,EACxD,KAAK,qBAAqB,IAAI,CAAC,6BAA6B,IAAI,eAAe,EAChF,GAAG,CAAC,CAAC,EACP,GAAG,CAAC,CAAC,EACN;QACA,IAAI,CAAC,cAAc,gBAAgB;YACjC,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI;QACJ,IAAI;QACJ,MAAM,OAAO;QACb,MAAM,SAAS;YAAE,KAAK;YAAkB,KAAK;QAAsB;QACnE,MAAM,UAAU,KAAK,SAAS,CAC5B,SAAS,CAAC,GAAG,eAAe;YAC1B,KAAK,IAAI,CAAC,SAAS;YACnB,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM;YACvB,WAAW,IAAI,CAAC,SAAS;YACzB,KAAK;YACL,KAAK;YACL,KAAK,OAAO;YACZ,GAAI,IAAI,CAAC,IAAI,KAAK;gBAAE,KAAK;YAAK,IAAI,SAAS;QAC7C;QAEF,IAAI,qBAAqB,QAAQ;YAC/B,SAAS;gBAAC,UAAU,MAAM,CAAC,KAAK,SAAS,CAAC;gBAAU,UAAU,MAAM,CAAC;gBAAU;aAAG,CAAC,IAAI,CAAC;QAC1F,OAAO;YACL,MAAM,YAAY,iBAAiB,UAAU,CAAC;YAC9C,IAAI,WAAW;gBACb,MAAM,IAAI,CAAC,YAAY,CAAC;YAC1B,OAAO;gBACL,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,IAAI;gBAEzC,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,UACR,CAAC,sDAAsD,EAAE,kBAAkB;gBAE/E;gBACA,MAAM,SAAS,GAAG,CAAC;oBAAE,KAAK;oBAAkB,KAAK;gBAAM;gBACvD,IAAI,CAAC,KAAK;oBACR,MAAM,IAAI,UAAU,CAAC,kCAAkC,EAAE,kBAAkB;gBAC7E;YACF;YAEA,SAAS,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,cAAc,MAAM,CAAC,UAC1D,kBAAkB,CAAC;gBAClB,GAAG,MAAM;gBACT,KAAK,YAAY,YAAY,IAAI,GAAG,CAAC,GAAG;YAC1C,GACC,IAAI,CAAC,YAAY,MAAM,MAAM,IAAI,SAAS,CAAC;QAChD;QAEA,IAAI,CAAC,gBAAgB;YACnB,OAAO;QACT;QAEA,MAAM,SAAS;YAAE,KAAK;YAAgB,KAAK;YAAoB,KAAK;QAAsB;QAE1F,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,gBAAgB;YACnC,CAAC,IAAI,GAAG,MAAM,cAAc,IAAI,CAC9B,IAAI,CAAC,MAAM,EACX;gBAAE,KAAK,OAAO,GAAG;gBAAE,KAAK;YAAM,GAC9B;gBAAE,YAAY;YAAK;QAEvB,OAAO;YACL,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,QAAQ,OAAO,GAAG,GAAG,OAAO,GAAG;QACxE;QAEA,OAAO,IAAI,KAAK,cAAc,CAAC,IAAI,cAAc,MAAM,CAAC,SACrD,kBAAkB,CAAC;YAClB,GAAG,MAAM;YACT,KAAK,eAAe,aAAa,YAAY,IAAI,GAAG,CAAC,GAAG;QAC1D,GACC,OAAO,CAAC,eAAe,aAAa,MAAM,MAAM,IAAI,SAAS,CAAC,OAAO,GAAG;IAC7E;IAEA,MAAM,2BAA2B,SAAS,CAAC,CAAC,EAAE,EAAE,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;QAC7E,0BAA0B,IAAI,CAAC,MAAM,EAAE;QAEvC,MAAM,OAAO;YACX,GAAI,aAAa,SAAS,SAAS,oBAAoB,IAAI,CAAC,IAAI,EAAE,OAAO;YACzE,WAAW,IAAI,CAAC,SAAS;QAC3B;QAEA,MAAM,WAAW,MAAM,kBAAkB,IAAI,CAC3C,IAAI,EACJ,gCACA;YACE,cAAc;YACd,MAAM;QACR,GACA;YAAE;YAAwB,oBAAoB;QAAQ;QAExD,MAAM,eAAe,gBAAgB,UAAU;YAAE,YAAY;QAAI;QAEjE,IAAI,CAAC,CAAC,gBAAgB,YAAY,GAAG;YACnC,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;YACF;QACF;QACA,IAAI,OAAO,aAAa,UAAU,KAAK,UAAU;YAC/C,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;YACF;QACF;QACA,IAAI,CAAC,CAAC,iBAAiB,YAAY,GAAG;YACpC,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;YACF;QACF;QACA,IAAI,OAAO,aAAa,WAAW,KAAK,UAAU;YAChD,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;YACF;QACF;QAEA,OAAO;IACT;IAEA,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,CAAA,MAAO;IACrB;IAEA,wBAAwB,GACxB,CAAC,QAAQ,MAAM,CAAC,GAAG;QACjB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACxD,OAAO;YACP,QAAQ,QAAQ,MAAM,CAAC,KAAK;YAC5B,SAAS;YACT,QAAQ;QACV,IAAI;IACN;IAEA,OAAO;QACL,OAAO,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK;IACnC;IAEA,QAAQ;QACN,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK;IACnC;IAEA,QAAQ;QACN,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK;IACnC;IAEA,MAAM,aAAa,QAAQ,EAAE;QAC3B,MAAM,cAAc,IAAI,CAAC,iCAAiC;QAC1D,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,aAAa;YAAC;YAAO;YAAO;SAAM;QACvF,OAAO,OAAO;IAChB;IAEA;;;GAGC,GACD,MAAM,UAAU,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE;QACrD,IAAI,CAAC,cAAc,UAAU;YAC3B,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI;QACJ,IAAI,YAAY,kBAAkB;YAChC,aAAa;QACf,OAAO,IAAI,eAAe,CAAC,OAAO,WAAW,CAAC,KAAK,aAAa;YAC9D,aAAa;QACf,OAAO,IAAI,KAAK,aAAa,KAAK,eAAe;YAC/C,aAAa,OAAO,gBAAgB,CAAC;QACvC,OAAO;YACL,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,WAAW,IAAI,KAAK,WAAW;YACjC,MAAM,IAAI,UAAU;QACtB;QACA,IAAI,MAAM,uBAAuB,IAAI,CAAC,IAAI,EAAE,YAAY;QAExD,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,IAAI,KAAK,OAAO,CAAC;YACtB,KAAK,cACD,UAAU,MAAM,CAAC,OAAO,UAAU,CAAC,UAAU,MAAM,CAAC,aAAa,MAAM,MACvE;YACJ,GAAG,OAAO;QACZ,GACG,kBAAkB,CAAC;YAClB;YACA,KAAK;YACL,KAAK,MAAM,OAAO,YAAY;QAChC,GACC,WAAW,GACX,MAAM,CAAC,UACP,IAAI,CAAC;IACV;AACF;AAEA,SAAS,oCAAoC,SAAS;IACpD,OAAQ,UAAU,SAAS,CAAC,IAAI;QAC9B,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YAAS;gBACZ,OAAQ,UAAU,SAAS,CAAC,UAAU;oBACpC,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT;wBACE;gBACJ;gBACA;YACF;QACA,KAAK;YACH,OAAO,CAAC,EAAE,EAAE,UAAU,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QACtD,KAAK;YACH,OAAO,CAAC,EAAE,EAAE,UAAU,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QACtD;YACE,MAAM,IAAI,UAAU;IACxB;AACF;AAEA,IAAI;AACJ,IAAI,KAAK,aAAa,KAAK,eAAe;IACxC,yBAAyB,SAAU,UAAU,EAAE,eAAe;QAC5D,IAAI,eAAe,CAAC,OAAO,WAAW,CAAC,KAAK,aAAa;YACvD,OAAO,oCAAoC;QAC7C;QAEA,OAAQ,WAAW,iBAAiB;YAClC,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,qBAAqB,YAAY;YAC1C,KAAK;YACL,KAAK,gBAAgB;gBACnB,OAAO,sBACL,YACA,iBACA,IAAI,CAAC,MAAM,CAAC,iCAAiC;YAEjD;gBACE,MAAM,IAAI,UAAU;QACxB;IACF;IAEA,MAAM,OAAO;IACb,SAAS,sBAAsB,UAAU,EAAE,eAAe,EAAE,eAAe;QACzE,IACE,OAAO,oBAAoB,YAC3B,gBAAgB,MAAM,KAAK,SAC3B,gBAAgB,GAAG,IACnB,gBAAgB,GAAG,CAAC,GAAG,EACvB;YACA,OAAO,gBAAgB,GAAG,CAAC,GAAG;QAChC;QAEA,IAAI,MAAM,OAAO,CAAC,kBAAkB;YAClC,IAAI,aAAa,gBAAgB,MAAM,CAAC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;YACnE,IAAI,WAAW,iBAAiB,KAAK,WAAW;gBAC9C,aAAa,WAAW,MAAM,CAAC,CAAC,QAAU,MAAM,UAAU,CAAC;YAC7D;YACA,OAAO;gBAAC;gBAAS;gBAAS;gBAAS;gBAAS;gBAAS;aAAQ,CAAC,IAAI,CAAC,CAAC,YAClE,WAAW,QAAQ,CAAC;QAExB;QAEA,OAAO;IACT;IAEA,MAAM,OAAO,OAAO,IAAI,CAAC;QAAC;QAAI;QAAK;QAAI;QAAK;QAAI;QAAG;QAAG;KAAE;IACxD,MAAM,OAAO,OAAO,IAAI,CAAC;QAAC;QAAI;QAAK;QAAG;QAAG;KAAG;IAC5C,MAAM,OAAO,OAAO,IAAI,CAAC;QAAC;QAAI;QAAK;QAAG;QAAG;KAAG;IAC5C,MAAM,YAAY,OAAO,IAAI,CAAC;QAAC;QAAI;QAAK;QAAG;QAAG;KAAG;IAEjD,SAAS,qBAAqB,UAAU,EAAE,eAAe;QACvD,qBAAqB;QACrB,OACE,OAAO,oBAAoB,YAC3B,OAAO,gBAAgB,GAAG,KAAK,YAC/B,gBAAgB,GAAG,CAAC,GAAG;YAEvB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE;QACJ;QAEA,MAAM,MAAM,WAAW,MAAM,CAAC;YAAE,QAAQ;YAAO,MAAM;QAAQ;QAC7D,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,KAAK;QAC9B,MAAM,MAAM,GAAG,CAAC,EAAE;QAClB,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI;QAC1C,IAAI,SAAS,MAAM,CAAC,OAAO;YACzB,OAAO;QACT;QAEA,IAAI,SAAS,MAAM,CAAC,OAAO;YACzB,OAAO;QACT;QACA,IAAI,SAAS,MAAM,CAAC,OAAO;YACzB,OAAO;QACT;QAEA,IAAI,SAAS,MAAM,CAAC,YAAY;YAC9B,OAAO;QACT;QAEA,MAAM,IAAI,UAAU;IACtB;AACF,OAAO;IACL,yBAAyB;AAC3B;AAEA,MAAM,WAAW,IAAI;AACrB,eAAe,OAAO,SAAS,EAAE,eAAe;IAC9C,IACE,KAAK,aAAa,KAAK,iBACvB,OAAO,oBAAoB,YAC3B,OAAO,gBAAgB,GAAG,KAAK,YAC/B,gBAAgB,MAAM,KAAK,OAC3B;QACA,OAAO,KAAK,gBAAgB,GAAG,EAAE,OAAO,OAAO,KAAK,KAAK,KAAK;IAChE;IAEA,IAAI,SAAS,GAAG,CAAC,kBAAkB;QACjC,OAAO,SAAS,GAAG,CAAC;IACtB;IAEA,MAAM,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,YAAY,OAAO,OAAO,KAAK,KAAK,KAAK;IAE/E,IAAI,YAAY,oBAAoB,KAAK,aAAa,KAAK,gBAAgB;QACzE,SAAS,GAAG,CAAC,iBAAiB;IAChC;IAEA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG,CAAC,QAAQ,mBAAmB,KAAK,GAChD,MAAM,eAAe;QACnB,YAAY,GAAG,IAAI,CAAE;YACnB,KAAK,CAAC,QAAQ,qBAAqB;QACrC;QAEA,WAAW,SAAS;YAClB,OAAO;QACT;IACF;AAEF,OAAO,OAAO,CAAC,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2852, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/issuer_registry.js"], "sourcesContent": ["const LRU = require('lru-cache');\n\nmodule.exports = new LRU({ max: 100 });\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,OAAO,OAAO,GAAG,IAAI,IAAI;IAAE,KAAK;AAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2859, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/helpers/webfinger_normalize.js"], "sourcesContent": ["// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\n\nconst PORT = /^\\d+$/;\n\nfunction hasScheme(input) {\n  if (input.includes('://')) return true;\n\n  const authority = input.replace(/(\\/|\\?)/g, '#').split('#')[0];\n  if (authority.includes(':')) {\n    const index = authority.indexOf(':');\n    const hostOrPort = authority.slice(index + 1);\n    if (!PORT.test(hostOrPort)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction acctSchemeAssumed(input) {\n  if (!input.includes('@')) return false;\n  const parts = input.split('@');\n  const host = parts[parts.length - 1];\n  return !(host.includes(':') || host.includes('/') || host.includes('?'));\n}\n\nfunction normalize(input) {\n  if (typeof input !== 'string') {\n    throw new TypeError('input must be a string');\n  }\n\n  let output;\n  if (hasScheme(input)) {\n    output = input;\n  } else if (acctSchemeAssumed(input)) {\n    output = `acct:${input}`;\n  } else {\n    output = `https://${input}`;\n  }\n\n  return output.split('#')[0];\n}\n\nmodule.exports = normalize;\n"], "names": [], "mappings": "AAAA,gFAAgF;AAEhF,sBAAsB;AACtB,+EAA+E;AAC/E,2EAA2E;AAC3E,4EAA4E;AAC5E,+CAA+C;AAC/C,EAAE;AACF,0EAA0E;AAC1E,uEAAuE;AACvE,sEAAsE;AACtE,4EAA4E;AAC5E,+EAA+E;AAC/E,6EAA6E;AAC7E,gFAAgF;AAChF,oEAAoE;AACpE,sDAAsD;AACtD,8EAA8E;AAC9E,8EAA8E;AAC9E,UAAU;AACV,4EAA4E;AAC5E,sDAAsD;AACtD,0EAA0E;AAC1E,oEAAoE;AACpE,EAAE;AACF,mEAAmE;AACnE,uEAAuE;AACvE,oEAAoE;AAEpE,MAAM,OAAO;AAEb,SAAS,UAAU,KAAK;IACtB,IAAI,MAAM,QAAQ,CAAC,QAAQ,OAAO;IAElC,MAAM,YAAY,MAAM,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;IAC9D,IAAI,UAAU,QAAQ,CAAC,MAAM;QAC3B,MAAM,QAAQ,UAAU,OAAO,CAAC;QAChC,MAAM,aAAa,UAAU,KAAK,CAAC,QAAQ;QAC3C,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa;YAC1B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,OAAO;IACjC,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IACpC,OAAO,CAAC,CAAC,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,IAAI;AACzE;AAEA,SAAS,UAAU,KAAK;IACtB,IAAI,OAAO,UAAU,UAAU;QAC7B,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI;IACJ,IAAI,UAAU,QAAQ;QACpB,SAAS;IACX,OAAO,IAAI,kBAAkB,QAAQ;QACnC,SAAS,CAAC,KAAK,EAAE,OAAO;IAC1B,OAAO;QACL,SAAS,CAAC,QAAQ,EAAE,OAAO;IAC7B;IAEA,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;AAC7B;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2923, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/issuer.js"], "sourcesContent": ["const { inspect } = require('util');\nconst url = require('url');\n\nconst { RPError } = require('./errors');\nconst getClient = require('./client');\nconst registry = require('./issuer_registry');\nconst processResponse = require('./helpers/process_response');\nconst webfingerNormalize = require('./helpers/webfinger_normalize');\nconst request = require('./helpers/request');\nconst clone = require('./helpers/deep_clone');\nconst { keystore } = require('./helpers/issuer');\n\nconst AAD_MULTITENANT_DISCOVERY = [\n  'https://login.microsoftonline.com/common/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration',\n];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n  claim_types_supported: ['normal'],\n  claims_parameter_supported: false,\n  grant_types_supported: ['authorization_code', 'implicit'],\n  request_parameter_supported: false,\n  request_uri_parameter_supported: true,\n  require_request_uri_registration: false,\n  response_modes_supported: ['query', 'fragment'],\n  token_endpoint_auth_methods_supported: ['client_secret_basic'],\n};\n\nclass Issuer {\n  #metadata;\n  constructor(meta = {}) {\n    const aadIssValidation = meta[AAD_MULTITENANT];\n    delete meta[AAD_MULTITENANT];\n    ['introspection', 'revocation'].forEach((endpoint) => {\n      // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n      // are defined\n      if (\n        meta[`${endpoint}_endpoint`] &&\n        meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined &&\n        meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined\n      ) {\n        if (meta.token_endpoint_auth_methods_supported) {\n          meta[`${endpoint}_endpoint_auth_methods_supported`] =\n            meta.token_endpoint_auth_methods_supported;\n        }\n        if (meta.token_endpoint_auth_signing_alg_values_supported) {\n          meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] =\n            meta.token_endpoint_auth_signing_alg_values_supported;\n        }\n      }\n    });\n\n    this.#metadata = new Map();\n\n    Object.entries(meta).forEach(([key, value]) => {\n      this.#metadata.set(key, value);\n      if (!this[key]) {\n        Object.defineProperty(this, key, {\n          get() {\n            return this.#metadata.get(key);\n          },\n          enumerable: true,\n        });\n      }\n    });\n\n    registry.set(this.issuer, this);\n\n    const Client = getClient(this, aadIssValidation);\n\n    Object.defineProperties(this, {\n      Client: { value: Client, enumerable: true },\n      FAPI1Client: { value: class FAPI1Client extends Client {}, enumerable: true },\n      FAPI2Client: { value: class FAPI2Client extends Client {}, enumerable: true },\n    });\n  }\n\n  get metadata() {\n    return clone(Object.fromEntries(this.#metadata.entries()));\n  }\n\n  static async webfinger(input) {\n    const resource = webfingerNormalize(input);\n    const { host } = url.parse(resource);\n    const webfingerUrl = `https://${host}/.well-known/webfinger`;\n\n    const response = await request.call(this, {\n      method: 'GET',\n      url: webfingerUrl,\n      responseType: 'json',\n      searchParams: { resource, rel: 'http://openid.net/specs/connect/1.0/issuer' },\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n\n    const location =\n      Array.isArray(body.links) &&\n      body.links.find(\n        (link) =>\n          typeof link === 'object' &&\n          link.rel === 'http://openid.net/specs/connect/1.0/issuer' &&\n          link.href,\n      );\n\n    if (!location) {\n      throw new RPError({\n        message: 'no issuer found in webfinger response',\n        body,\n      });\n    }\n\n    if (typeof location.href !== 'string' || !location.href.startsWith('https://')) {\n      throw new RPError({\n        printf: ['invalid issuer location %s', location.href],\n        body,\n      });\n    }\n\n    const expectedIssuer = location.href;\n    if (registry.has(expectedIssuer)) {\n      return registry.get(expectedIssuer);\n    }\n\n    const issuer = await this.discover(expectedIssuer);\n\n    if (issuer.issuer !== expectedIssuer) {\n      registry.del(issuer.issuer);\n      throw new RPError(\n        'discovered issuer mismatch, expected %s, got: %s',\n        expectedIssuer,\n        issuer.issuer,\n      );\n    }\n    return issuer;\n  }\n\n  static async discover(uri) {\n    const wellKnownUri = resolveWellKnownUri(uri);\n\n    const response = await request.call(this, {\n      method: 'GET',\n      responseType: 'json',\n      url: wellKnownUri,\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n    return new Issuer({\n      ...ISSUER_DEFAULTS,\n      ...body,\n      [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find((discoveryURL) =>\n        wellKnownUri.startsWith(discoveryURL),\n      ),\n    });\n  }\n\n  async reloadJwksUri() {\n    await keystore.call(this, true);\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.metadata, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nfunction resolveWellKnownUri(uri) {\n  const parsed = url.parse(uri);\n  if (parsed.pathname.includes('/.well-known/')) {\n    return uri;\n  } else {\n    let pathname;\n    if (parsed.pathname.endsWith('/')) {\n      pathname = `${parsed.pathname}.well-known/openid-configuration`;\n    } else {\n      pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n    }\n    return url.format({ ...parsed, pathname });\n  }\n}\n\nmodule.exports = Issuer;\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,OAAO,EAAE;AACjB,MAAM;AAEN,MAAM,EAAE,OAAO,EAAE;AACjB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM,4BAA4B;IAChC;IACA;IACA;IACA;CACD;AACD,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;IACtB,uBAAuB;QAAC;KAAS;IACjC,4BAA4B;IAC5B,uBAAuB;QAAC;QAAsB;KAAW;IACzD,6BAA6B;IAC7B,iCAAiC;IACjC,kCAAkC;IAClC,0BAA0B;QAAC;QAAS;KAAW;IAC/C,uCAAuC;QAAC;KAAsB;AAChE;AAEA,MAAM;IACJ,CAAA,QAAS,CAAC;IACV,YAAY,OAAO,CAAC,CAAC,CAAE;QACrB,MAAM,mBAAmB,IAAI,CAAC,gBAAgB;QAC9C,OAAO,IAAI,CAAC,gBAAgB;QAC5B;YAAC;YAAiB;SAAa,CAAC,OAAO,CAAC,CAAC;YACvC,wFAAwF;YACxF,cAAc;YACd,IACE,IAAI,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC,IAC5B,IAAI,CAAC,GAAG,SAAS,gCAAgC,CAAC,CAAC,KAAK,aACxD,IAAI,CAAC,GAAG,SAAS,2CAA2C,CAAC,CAAC,KAAK,WACnE;gBACA,IAAI,KAAK,qCAAqC,EAAE;oBAC9C,IAAI,CAAC,GAAG,SAAS,gCAAgC,CAAC,CAAC,GACjD,KAAK,qCAAqC;gBAC9C;gBACA,IAAI,KAAK,gDAAgD,EAAE;oBACzD,IAAI,CAAC,GAAG,SAAS,2CAA2C,CAAC,CAAC,GAC5D,KAAK,gDAAgD;gBACzD;YACF;QACF;QAEA,IAAI,CAAC,CAAA,QAAS,GAAG,IAAI;QAErB,OAAO,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACxC,IAAI,CAAC,CAAA,QAAS,CAAC,GAAG,CAAC,KAAK;YACxB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACd,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;oBAC/B;wBACE,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,GAAG,CAAC;oBAC5B;oBACA,YAAY;gBACd;YACF;QACF;QAEA,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI;QAE9B,MAAM,SAAS,UAAU,IAAI,EAAE;QAE/B,OAAO,gBAAgB,CAAC,IAAI,EAAE;YAC5B,QAAQ;gBAAE,OAAO;gBAAQ,YAAY;YAAK;YAC1C,aAAa;gBAAE,OAAO,MAAM,oBAAoB;gBAAQ;gBAAG,YAAY;YAAK;YAC5E,aAAa;gBAAE,OAAO,MAAM,oBAAoB;gBAAQ;gBAAG,YAAY;YAAK;QAC9E;IACF;IAEA,IAAI,WAAW;QACb,OAAO,MAAM,OAAO,WAAW,CAAC,IAAI,CAAC,CAAA,QAAS,CAAC,OAAO;IACxD;IAEA,aAAa,UAAU,KAAK,EAAE;QAC5B,MAAM,WAAW,mBAAmB;QACpC,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC;QAC3B,MAAM,eAAe,CAAC,QAAQ,EAAE,KAAK,sBAAsB,CAAC;QAE5D,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC,IAAI,EAAE;YACxC,QAAQ;YACR,KAAK;YACL,cAAc;YACd,cAAc;gBAAE;gBAAU,KAAK;YAA6C;YAC5E,SAAS;gBACP,QAAQ;YACV;QACF;QACA,MAAM,OAAO,gBAAgB;QAE7B,MAAM,WACJ,MAAM,OAAO,CAAC,KAAK,KAAK,KACxB,KAAK,KAAK,CAAC,IAAI,CACb,CAAC,OACC,OAAO,SAAS,YAChB,KAAK,GAAG,KAAK,gDACb,KAAK,IAAI;QAGf,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,QAAQ;gBAChB,SAAS;gBACT;YACF;QACF;QAEA,IAAI,OAAO,SAAS,IAAI,KAAK,YAAY,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,aAAa;YAC9E,MAAM,IAAI,QAAQ;gBAChB,QAAQ;oBAAC;oBAA8B,SAAS,IAAI;iBAAC;gBACrD;YACF;QACF;QAEA,MAAM,iBAAiB,SAAS,IAAI;QACpC,IAAI,SAAS,GAAG,CAAC,iBAAiB;YAChC,OAAO,SAAS,GAAG,CAAC;QACtB;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC;QAEnC,IAAI,OAAO,MAAM,KAAK,gBAAgB;YACpC,SAAS,GAAG,CAAC,OAAO,MAAM;YAC1B,MAAM,IAAI,QACR,oDACA,gBACA,OAAO,MAAM;QAEjB;QACA,OAAO;IACT;IAEA,aAAa,SAAS,GAAG,EAAE;QACzB,MAAM,eAAe,oBAAoB;QAEzC,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC,IAAI,EAAE;YACxC,QAAQ;YACR,cAAc;YACd,KAAK;YACL,SAAS;gBACP,QAAQ;YACV;QACF;QACA,MAAM,OAAO,gBAAgB;QAC7B,OAAO,IAAI,OAAO;YAChB,GAAG,eAAe;YAClB,GAAG,IAAI;YACP,CAAC,gBAAgB,EAAE,CAAC,CAAC,0BAA0B,IAAI,CAAC,CAAC,eACnD,aAAa,UAAU,CAAC;QAE5B;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,SAAS,IAAI,CAAC,IAAI,EAAE;IAC5B;IAEA,wBAAwB,GACxB,CAAC,QAAQ,MAAM,CAAC,GAAG;QACjB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACxD,OAAO;YACP,QAAQ,QAAQ,MAAM,CAAC,KAAK;YAC5B,SAAS;YACT,QAAQ;QACV,IAAI;IACN;AACF;AAEA,SAAS,oBAAoB,GAAG;IAC9B,MAAM,SAAS,IAAI,KAAK,CAAC;IACzB,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,kBAAkB;QAC7C,OAAO;IACT,OAAO;QACL,IAAI;QACJ,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM;YACjC,WAAW,GAAG,OAAO,QAAQ,CAAC,gCAAgC,CAAC;QACjE,OAAO;YACL,WAAW,GAAG,OAAO,QAAQ,CAAC,iCAAiC,CAAC;QAClE;QACA,OAAO,IAAI,MAAM,CAAC;YAAE,GAAG,MAAM;YAAE;QAAS;IAC1C;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/passport_strategy.js"], "sourcesContent": ["const url = require('url');\nconst { format } = require('util');\n\nconst cloneDeep = require('./helpers/deep_clone');\nconst { RPError, OPError } = require('./errors');\nconst { BaseClient } = require('./client');\nconst { random, codeChallenge } = require('./helpers/generators');\nconst pick = require('./helpers/pick');\nconst { resolveResponseType, resolveRedirectUri } = require('./helpers/client');\n\nfunction verified(err, user, info = {}) {\n  if (err) {\n    this.error(err);\n  } else if (!user) {\n    this.fail(info);\n  } else {\n    this.success(user, info);\n  }\n}\n\nfunction OpenIDConnectStrategy(\n  { client, params = {}, passReqToCallback = false, sessionKey, usePKCE = true, extras = {} } = {},\n  verify,\n) {\n  if (!(client instanceof BaseClient)) {\n    throw new TypeError('client must be an instance of openid-client Client');\n  }\n\n  if (typeof verify !== 'function') {\n    throw new TypeError('verify callback must be a function');\n  }\n\n  if (!client.issuer || !client.issuer.issuer) {\n    throw new TypeError('client must have an issuer with an identifier');\n  }\n\n  this._client = client;\n  this._issuer = client.issuer;\n  this._verify = verify;\n  this._passReqToCallback = passReqToCallback;\n  this._usePKCE = usePKCE;\n  this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n  this._params = cloneDeep(params);\n\n  // state and nonce are handled in authenticate()\n  delete this._params.state;\n  delete this._params.nonce;\n\n  this._extras = cloneDeep(extras);\n\n  if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n  if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n  if (!this._params.scope) this._params.scope = 'openid';\n\n  if (this._usePKCE === true) {\n    const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported)\n      ? this._issuer.code_challenge_methods_supported\n      : false;\n\n    if (supportedMethods && supportedMethods.includes('S256')) {\n      this._usePKCE = 'S256';\n    } else if (supportedMethods && supportedMethods.includes('plain')) {\n      this._usePKCE = 'plain';\n    } else if (supportedMethods) {\n      throw new TypeError(\n        'neither code_challenge_method supported by the client is supported by the issuer',\n      );\n    } else {\n      this._usePKCE = 'S256';\n    }\n  } else if (typeof this._usePKCE === 'string' && !['plain', 'S256'].includes(this._usePKCE)) {\n    throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n  }\n\n  this.name = url.parse(client.issuer.issuer).hostname;\n}\n\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n  (async () => {\n    const client = this._client;\n    if (!req.session) {\n      throw new TypeError('authentication requires session support');\n    }\n    const reqParams = client.callbackParams(req);\n    const sessionKey = this._key;\n\n    const { 0: parameter, length } = Object.keys(reqParams);\n\n    /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */\n    if (length === 0 || (length === 1 && parameter === 'iss')) {\n      // provide options object with extra authentication parameters\n      const params = {\n        state: random(),\n        ...this._params,\n        ...options,\n      };\n\n      if (!params.nonce && params.response_type.includes('id_token')) {\n        params.nonce = random();\n      }\n\n      req.session[sessionKey] = pick(params, 'nonce', 'state', 'max_age', 'response_type');\n\n      if (this._usePKCE && params.response_type.includes('code')) {\n        const verifier = random();\n        req.session[sessionKey].code_verifier = verifier;\n\n        switch (this._usePKCE) {\n          case 'S256':\n            params.code_challenge = codeChallenge(verifier);\n            params.code_challenge_method = 'S256';\n            break;\n          case 'plain':\n            params.code_challenge = verifier;\n            break;\n        }\n      }\n\n      this.redirect(client.authorizationUrl(params));\n      return;\n    }\n    /* end authentication request */\n\n    /* start authentication response */\n\n    const session = req.session[sessionKey];\n    if (Object.keys(session || {}).length === 0) {\n      throw new Error(\n        format(\n          'did not find expected authorization request details in session, req.session[\"%s\"] is %j',\n          sessionKey,\n          session,\n        ),\n      );\n    }\n\n    const {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    } = session;\n\n    try {\n      delete req.session[sessionKey];\n    } catch (err) {}\n\n    const opts = {\n      redirect_uri: this._params.redirect_uri,\n      ...options,\n    };\n\n    const checks = {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    };\n\n    const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n\n    const passReq = this._passReqToCallback;\n    const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n\n    const args = [tokenset, verified.bind(this)];\n\n    if (loadUserinfo) {\n      if (!tokenset.access_token) {\n        throw new RPError({\n          message:\n            'expected access_token to be returned when asking for userinfo in verify callback',\n          tokenset,\n        });\n      }\n      const userinfo = await client.userinfo(tokenset);\n      args.splice(1, 0, userinfo);\n    }\n\n    if (passReq) {\n      args.unshift(req);\n    }\n\n    this._verify(...args);\n    /* end authentication response */\n  })().catch((error) => {\n    if (\n      (error instanceof OPError &&\n        error.error !== 'server_error' &&\n        !error.error.startsWith('invalid')) ||\n      error instanceof RPError\n    ) {\n      this.fail(error);\n    } else {\n      this.error(error);\n    }\n  });\n};\n\nmodule.exports = OpenIDConnectStrategy;\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM,EAAE,MAAM,EAAE;AAEhB,MAAM;AACN,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;AAC1B,MAAM,EAAE,UAAU,EAAE;AACpB,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE;AAC/B,MAAM;AACN,MAAM,EAAE,mBAAmB,EAAE,kBAAkB,EAAE;AAEjD,SAAS,SAAS,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACpC,IAAI,KAAK;QACP,IAAI,CAAC,KAAK,CAAC;IACb,OAAO,IAAI,CAAC,MAAM;QAChB,IAAI,CAAC,IAAI,CAAC;IACZ,OAAO;QACL,IAAI,CAAC,OAAO,CAAC,MAAM;IACrB;AACF;AAEA,SAAS,sBACP,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE,oBAAoB,KAAK,EAAE,UAAU,EAAE,UAAU,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAChG,MAAM;IAEN,IAAI,CAAC,CAAC,kBAAkB,UAAU,GAAG;QACnC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,OAAO,WAAW,YAAY;QAChC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,MAAM,EAAE;QAC3C,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM;IAC5B,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE;IAC3E,IAAI,CAAC,OAAO,GAAG,UAAU;IAEzB,gDAAgD;IAChD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;IACzB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;IAEzB,IAAI,CAAC,OAAO,GAAG,UAAU;IAEzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,oBAAoB,IAAI,CAAC;IACvF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,mBAAmB,IAAI,CAAC;IACpF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;IAE9C,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;QAC1B,MAAM,mBAAmB,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,gCAAgC,IAChF,IAAI,CAAC,OAAO,CAAC,gCAAgC,GAC7C;QAEJ,IAAI,oBAAoB,iBAAiB,QAAQ,CAAC,SAAS;YACzD,IAAI,CAAC,QAAQ,GAAG;QAClB,OAAO,IAAI,oBAAoB,iBAAiB,QAAQ,CAAC,UAAU;YACjE,IAAI,CAAC,QAAQ,GAAG;QAClB,OAAO,IAAI,kBAAkB;YAC3B,MAAM,IAAI,UACR;QAEJ,OAAO;YACL,IAAI,CAAC,QAAQ,GAAG;QAClB;IACF,OAAO,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,YAAY,CAAC;QAAC;QAAS;KAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG;QAC1F,MAAM,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,oDAAoD,CAAC;IAC5F;IAEA,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,OAAO,MAAM,CAAC,MAAM,EAAE,QAAQ;AACtD;AAEA,sBAAsB,SAAS,CAAC,YAAY,GAAG,SAAS,aAAa,GAAG,EAAE,OAAO;IAC/E,CAAC;QACC,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,IAAI,CAAC,IAAI,OAAO,EAAE;YAChB,MAAM,IAAI,UAAU;QACtB;QACA,MAAM,YAAY,OAAO,cAAc,CAAC;QACxC,MAAM,aAAa,IAAI,CAAC,IAAI;QAE5B,MAAM,EAAE,GAAG,SAAS,EAAE,MAAM,EAAE,GAAG,OAAO,IAAI,CAAC;QAE7C;;;;KAIC,GACD,IAAI,WAAW,KAAM,WAAW,KAAK,cAAc,OAAQ;YACzD,8DAA8D;YAC9D,MAAM,SAAS;gBACb,OAAO;gBACP,GAAG,IAAI,CAAC,OAAO;gBACf,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,OAAO,KAAK,IAAI,OAAO,aAAa,CAAC,QAAQ,CAAC,aAAa;gBAC9D,OAAO,KAAK,GAAG;YACjB;YAEA,IAAI,OAAO,CAAC,WAAW,GAAG,KAAK,QAAQ,SAAS,SAAS,WAAW;YAEpE,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,aAAa,CAAC,QAAQ,CAAC,SAAS;gBAC1D,MAAM,WAAW;gBACjB,IAAI,OAAO,CAAC,WAAW,CAAC,aAAa,GAAG;gBAExC,OAAQ,IAAI,CAAC,QAAQ;oBACnB,KAAK;wBACH,OAAO,cAAc,GAAG,cAAc;wBACtC,OAAO,qBAAqB,GAAG;wBAC/B;oBACF,KAAK;wBACH,OAAO,cAAc,GAAG;wBACxB;gBACJ;YACF;YAEA,IAAI,CAAC,QAAQ,CAAC,OAAO,gBAAgB,CAAC;YACtC;QACF;QACA,8BAA8B,GAE9B,iCAAiC,GAEjC,MAAM,UAAU,IAAI,OAAO,CAAC,WAAW;QACvC,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,KAAK,GAAG;YAC3C,MAAM,IAAI,MACR,OACE,2FACA,YACA;QAGN;QAEA,MAAM,EACJ,KAAK,EACL,KAAK,EACL,SAAS,MAAM,EACf,eAAe,YAAY,EAC3B,eAAe,YAAY,EAC5B,GAAG;QAEJ,IAAI;YACF,OAAO,IAAI,OAAO,CAAC,WAAW;QAChC,EAAE,OAAO,KAAK,CAAC;QAEf,MAAM,OAAO;YACX,cAAc,IAAI,CAAC,OAAO,CAAC,YAAY;YACvC,GAAG,OAAO;QACZ;QAEA,MAAM,SAAS;YACb;YACA;YACA,SAAS;YACT,eAAe;YACf,eAAe;QACjB;QAEA,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC,KAAK,YAAY,EAAE,WAAW,QAAQ,IAAI,CAAC,OAAO;QAEzF,MAAM,UAAU,IAAI,CAAC,kBAAkB;QACvC,MAAM,eAAe,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,MAAM,CAAC,iBAAiB;QAE/F,MAAM,OAAO;YAAC;YAAU,SAAS,IAAI,CAAC,IAAI;SAAE;QAE5C,IAAI,cAAc;YAChB,IAAI,CAAC,SAAS,YAAY,EAAE;gBAC1B,MAAM,IAAI,QAAQ;oBAChB,SACE;oBACF;gBACF;YACF;YACA,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC;YACvC,KAAK,MAAM,CAAC,GAAG,GAAG;QACpB;QAEA,IAAI,SAAS;YACX,KAAK,OAAO,CAAC;QACf;QAEA,IAAI,CAAC,OAAO,IAAI;IAChB,+BAA+B,GACjC,CAAC,IAAI,KAAK,CAAC,CAAC;QACV,IACE,AAAC,iBAAiB,WAChB,MAAM,KAAK,KAAK,kBAChB,CAAC,MAAM,KAAK,CAAC,UAAU,CAAC,cAC1B,iBAAiB,SACjB;YACA,IAAI,CAAC,IAAI,CAAC;QACZ,OAAO;YACL,IAAI,CAAC,KAAK,CAAC;QACb;IACF;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3260, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/openid-client/lib/index.js"], "sourcesContent": ["const Issuer = require('./issuer');\nconst { OPError, RPError } = require('./errors');\nconst Strategy = require('./passport_strategy');\nconst TokenSet = require('./token_set');\nconst { CLOCK_TOLERANCE, HTTP_OPTIONS } = require('./helpers/consts');\nconst generators = require('./helpers/generators');\nconst { setDefaults } = require('./helpers/request');\n\nmodule.exports = {\n  Issuer,\n  Strategy,\n  TokenSet,\n  errors: {\n    OPError,\n    RPError,\n  },\n  custom: {\n    setHttpOptionsDefaults: setDefaults,\n    http_options: HTTP_OPTIONS,\n    clock_tolerance: CLOCK_TOLERANCE,\n  },\n  generators,\n};\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;AAC1B,MAAM;AACN,MAAM;AACN,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE;AACvC,MAAM;AACN,MAAM,EAAE,WAAW,EAAE;AAErB,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA,QAAQ;QACN;QACA;IACF;IACA,QAAQ;QACN,wBAAwB;QACxB,cAAc;QACd,iBAAiB;IACnB;IACA;AACF", "ignoreList": [0], "debugId": null}}]}