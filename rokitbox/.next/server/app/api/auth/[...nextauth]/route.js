var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/auth/[...nextauth]/route.js")
R.c("server/chunks/63ef3_next_cf1b6e22._.js")
R.c("server/chunks/63ef3_next-auth_25e1a3ce._.js")
R.c("server/chunks/63ef3_openid-client_bd25e519._.js")
R.c("server/chunks/63ef3_jose_dist_node_cjs_26484aab._.js")
R.c("server/chunks/63ef3_654cc9f7._.js")
R.c("server/chunks/[root-of-the-server]__a8567fcb._.js")
R.m("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
