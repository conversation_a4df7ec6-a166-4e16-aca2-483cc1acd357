var R=require("../../../chunks/ssr/[turbopack]_runtime.js")("server/app/auth/signin/page.js")
R.c("server/chunks/ssr/63ef3_49ef3075._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/Documents_Tech_augment-projects_RokitBox_rokitbox_src_app_03cf7c36._.js")
R.c("server/chunks/ssr/[root-of-the-server]__4b4ae336._.js")
R.c("server/chunks/ssr/63ef3_next_dist_client_components_628b527b._.js")
R.c("server/chunks/ssr/63ef3_next_dist_client_components_builtin_forbidden_4a39b043.js")
R.c("server/chunks/ssr/63ef3_next_dist_client_components_builtin_unauthorized_8f0619e1.js")
R.c("server/chunks/ssr/63ef3_next_dist_client_components_builtin_global-error_73c37063.js")
R.c("server/chunks/ssr/63ef3_next_dist_d21842f2._.js")
R.c("server/chunks/ssr/[root-of-the-server]__d4ab1042._.js")
R.m("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/.next-internal/server/app/auth/signin/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/esm/build/templates/app-page.js?page=/auth/signin/page { GLOBAL_ERROR_MODULE => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/esm/build/templates/app-page.js?page=/auth/signin/page { GLOBAL_ERROR_MODULE => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/src/app/auth/signin/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
