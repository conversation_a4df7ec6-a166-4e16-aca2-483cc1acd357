module.exports = [
"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/63ef3_ca07f5f6._.js",
  "build/chunks/[root-of-the-server]__0014e9f1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Documents/Tech/augment-projects/RokitBox/rokitbox/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),
];