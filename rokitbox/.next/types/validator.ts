// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/auth/signin/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/auth/signin">> = Specific
  const handler = {} as typeof import("../../src/app/auth/signin/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/devices/[id]/edit/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/devices/[id]/edit">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/devices/[id]/edit/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/devices/[id]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/devices/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/devices/[id]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/devices/new/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/devices/new">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/devices/new/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/settings/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/settings">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/settings/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/users/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/users">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/users/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/admin/users/[id]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/admin/users/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/api/admin/users/[id]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/admin/users/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/admin/users">> = Specific
  const handler = {} as typeof import("../../src/app/api/admin/users/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/auth/[...nextauth]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/auth/[...nextauth]">> = Specific
  const handler = {} as typeof import("../../src/app/api/auth/[...nextauth]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/devices/[id]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/devices/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/api/devices/[id]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/devices/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/devices">> = Specific
  const handler = {} as typeof import("../../src/app/api/devices/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}





// Validate ../../src/app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
