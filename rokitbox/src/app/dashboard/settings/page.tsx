"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import DashboardLayout from "@/components/layout/DashboardLayout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card"
import { Button } from "@/components/ui/Button"
import { Input } from "@/components/ui/Input"

export default function SettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [profileData, setProfileData] = useState({
    name: '',
    email: ''
  })

  useEffect(() => {
    if (status === "loading") return
    if (!session) {
      router.push("/auth/signin")
      return
    }
    
    setProfileData({
      name: session.user.name || '',
      email: session.user.email || ''
    })
  }, [session, status, router])

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // TODO: Implement profile update API
      alert('Funkce aktualizace profilu bude implementována v další verzi')
    } catch (error) {
      console.error('Error updating profile:', error)
      alert('Došlo k chybě při aktualizaci profilu')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement password change
    alert('Funkce změny hesla bude implementována v další verzi')
  }

  if (status === "loading") {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-secondary-600">Načítání...</div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Nastavení</h1>
          <p className="mt-1 text-sm text-secondary-600">
            Spravujte svůj profil a nastavení účtu
          </p>
        </div>

        {/* Organization Info */}
        <Card>
          <CardHeader>
            <CardTitle>Informace o organizaci</CardTitle>
            <CardDescription>
              Základní informace o vaší organizaci
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-secondary-700">Název organizace</label>
              <p className="text-secondary-900 mt-1">{session?.user?.organizationName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-secondary-700">Vaše role</label>
              <p className="text-secondary-900 mt-1">
                {session?.user?.role === 'ADMIN' ? 'Administrátor' : 'Uživatel'}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Profile Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Profil</CardTitle>
            <CardDescription>
              Upravte své osobní údaje
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleProfileUpdate} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium text-secondary-700">
                  Jméno
                </label>
                <Input
                  id="name"
                  value={profileData.name}
                  onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Vaše jméno"
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium text-secondary-700">
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  value={profileData.email}
                  disabled
                  className="bg-secondary-50"
                />
                <p className="text-xs text-secondary-500">
                  Email nelze změnit. Kontaktujte administrátora.
                </p>
              </div>
              <div className="flex justify-end">
                <Button type="submit" disabled={loading}>
                  {loading ? 'Ukládání...' : 'Uložit změny'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Password Change */}
        <Card>
          <CardHeader>
            <CardTitle>Změna hesla</CardTitle>
            <CardDescription>
              Aktualizujte své heslo pro zvýšení bezpečnosti
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handlePasswordChange} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="currentPassword" className="text-sm font-medium text-secondary-700">
                  Současné heslo
                </label>
                <Input
                  id="currentPassword"
                  type="password"
                  placeholder="••••••••"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="newPassword" className="text-sm font-medium text-secondary-700">
                  Nové heslo
                </label>
                <Input
                  id="newPassword"
                  type="password"
                  placeholder="••••••••"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="text-sm font-medium text-secondary-700">
                  Potvrdit nové heslo
                </label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="••••••••"
                />
              </div>
              <div className="flex justify-end">
                <Button type="submit">
                  Změnit heslo
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* System Information */}
        <Card>
          <CardHeader>
            <CardTitle>Systémové informace</CardTitle>
            <CardDescription>
              Informace o aplikaci RokitBox
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-secondary-700">Verze aplikace</label>
              <p className="text-secondary-900 mt-1">1.0.0</p>
            </div>
            <div>
              <label className="text-sm font-medium text-secondary-700">Poslední přihlášení</label>
              <p className="text-secondary-900 mt-1">
                {new Date().toLocaleDateString('cs-CZ')} {new Date().toLocaleTimeString('cs-CZ')}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
