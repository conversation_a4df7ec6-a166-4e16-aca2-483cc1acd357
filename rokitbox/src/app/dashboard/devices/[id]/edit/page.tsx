"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import DashboardLayout from "@/components/layout/DashboardLayout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card"
import { Button } from "@/components/ui/Button"
import { Input } from "@/components/ui/Input"
import { Select } from "@/components/ui/Select"
import { Textarea } from "@/components/ui/Textarea"
import { ArrowLeftIcon } from "@heroicons/react/24/outline"
import { DeviceType, DeviceStatus } from "@prisma/client"

interface Device {
  id: string
  name: string
  description?: string
  type: DeviceType
  brand?: string
  model?: string
  serialNumber?: string
  location?: string
  status: DeviceStatus
  purchaseDate?: string
  warrantyExpiry?: string
  notes?: string
}

const deviceTypeOptions = [
  { value: DeviceType.COMPUTER, label: 'Počítač' },
  { value: DeviceType.LAPTOP, label: 'Notebook' },
  { value: DeviceType.MONITOR, label: 'Monitor' },
  { value: DeviceType.PRINTER, label: 'Tiskárna' },
  { value: DeviceType.PHONE, label: 'Telefon' },
  { value: DeviceType.TABLET, label: 'Tablet' },
  { value: DeviceType.SERVER, label: 'Server' },
  { value: DeviceType.NETWORK, label: 'Síťové zařízení' },
  { value: DeviceType.OTHER, label: 'Ostatní' },
]

const deviceStatusOptions = [
  { value: DeviceStatus.ACTIVE, label: 'Aktivní' },
  { value: DeviceStatus.INACTIVE, label: 'Neaktivní' },
  { value: DeviceStatus.MAINTENANCE, label: 'Údržba' },
  { value: DeviceStatus.RETIRED, label: 'Vyřazeno' },
]

export default function EditDevice({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: DeviceType.COMPUTER,
    brand: '',
    model: '',
    serialNumber: '',
    location: '',
    status: DeviceStatus.ACTIVE,
    purchaseDate: '',
    warrantyExpiry: '',
    notes: ''
  })

  useEffect(() => {
    fetchDevice()
  }, [params.id])

  const fetchDevice = async () => {
    try {
      const response = await fetch(`/api/devices/${params.id}`)
      if (response.ok) {
        const device: Device = await response.json()
        setFormData({
          name: device.name,
          description: device.description || '',
          type: device.type,
          brand: device.brand || '',
          model: device.model || '',
          serialNumber: device.serialNumber || '',
          location: device.location || '',
          status: device.status,
          purchaseDate: device.purchaseDate ? device.purchaseDate.split('T')[0] : '',
          warrantyExpiry: device.warrantyExpiry ? device.warrantyExpiry.split('T')[0] : '',
          notes: device.notes || ''
        })
      } else if (response.status === 404) {
        router.push('/dashboard')
      }
    } catch (error) {
      console.error("Error fetching device:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      const response = await fetch(`/api/devices/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        router.push(`/dashboard/devices/${params.id}`)
      } else {
        const error = await response.json()
        alert(`Chyba: ${error.error}`)
      }
    } catch (error) {
      console.error('Error updating device:', error)
      alert('Došlo k chybě při aktualizaci zařízení')
    } finally {
      setSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-secondary-600">Načítání...</div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/devices/${params.id}`)}
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Zpět na detail
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-secondary-900">Upravit zařízení</h1>
            <p className="text-sm text-secondary-600">
              Upravte informace o zařízení
            </p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>Informace o zařízení</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-medium text-secondary-700">
                    Název zařízení *
                  </label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Např. Dell OptiPlex 7090"
                    required
                    disabled={submitting}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="type" className="text-sm font-medium text-secondary-700">
                    Typ zařízení *
                  </label>
                  <Select
                    id="type"
                    name="type"
                    value={formData.type}
                    onChange={handleChange}
                    required
                    disabled={submitting}
                  >
                    {deviceTypeOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="description" className="text-sm font-medium text-secondary-700">
                  Popis
                </label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Stručný popis zařízení..."
                  rows={3}
                  disabled={submitting}
                />
              </div>

              {/* Device Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="brand" className="text-sm font-medium text-secondary-700">
                    Značka
                  </label>
                  <Input
                    id="brand"
                    name="brand"
                    value={formData.brand}
                    onChange={handleChange}
                    placeholder="Např. Dell, HP, Lenovo"
                    disabled={submitting}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="model" className="text-sm font-medium text-secondary-700">
                    Model
                  </label>
                  <Input
                    id="model"
                    name="model"
                    value={formData.model}
                    onChange={handleChange}
                    placeholder="Např. OptiPlex 7090"
                    disabled={submitting}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="serialNumber" className="text-sm font-medium text-secondary-700">
                    Sériové číslo
                  </label>
                  <Input
                    id="serialNumber"
                    name="serialNumber"
                    value={formData.serialNumber}
                    onChange={handleChange}
                    placeholder="Např. ABC123456789"
                    disabled={submitting}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="location" className="text-sm font-medium text-secondary-700">
                    Umístění
                  </label>
                  <Input
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleChange}
                    placeholder="Např. Kancelář 201"
                    disabled={submitting}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="status" className="text-sm font-medium text-secondary-700">
                  Stav zařízení
                </label>
                <Select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  disabled={submitting}
                >
                  {deviceStatusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </div>

              {/* Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="purchaseDate" className="text-sm font-medium text-secondary-700">
                    Datum nákupu
                  </label>
                  <Input
                    id="purchaseDate"
                    name="purchaseDate"
                    type="date"
                    value={formData.purchaseDate}
                    onChange={handleChange}
                    disabled={submitting}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="warrantyExpiry" className="text-sm font-medium text-secondary-700">
                    Konec záruky
                  </label>
                  <Input
                    id="warrantyExpiry"
                    name="warrantyExpiry"
                    type="date"
                    value={formData.warrantyExpiry}
                    onChange={handleChange}
                    disabled={submitting}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="notes" className="text-sm font-medium text-secondary-700">
                  Poznámky
                </label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  placeholder="Další poznámky k zařízení..."
                  rows={3}
                  disabled={submitting}
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push(`/dashboard/devices/${params.id}`)}
                  disabled={submitting}
                >
                  Zrušit
                </Button>
                <Button type="submit" disabled={submitting}>
                  {submitting ? 'Ukládání...' : 'Uložit změny'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
