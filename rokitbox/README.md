# RokitBox - Systém pro evidenci hardwaru

RokitBox je moderní webová aplikace pro evidenci a správu hardwarových zařízení s podporou multi-tenant architektury.

## Funkce

- 🔐 **Autentifikace** - <PERSON>zpe<PERSON><PERSON><PERSON> p<PERSON>šování bez registrace
- 🏢 **Multi-tenant** - Oddělené organizace s vlastními daty
- 💻 **Správa zařízení** - CRUD operace pro hardwarová zařízení
- 👥 **Správa uživatelů** - Admin panel pro správu uživatelů
- 🎨 **Moderní design** - Responzivní UI podle firemního loga
- 📱 **Mobilní podpora** - Optimalizováno pro všechna zařízení

## Technologie

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **Databáze**: SQLite s Prisma ORM
- **Autentifikace**: NextAuth.js
- **Email**: <PERSON><PERSON><PERSON><PERSON> (pro notifikace)

## Instalace a spuštění

### Požadavky
- Node.js 18+
- npm nebo yarn

### Kroky instalace

1. **Klonování projektu**
```bash
git clone <repository-url>
cd rokitbox
```

2. **Instalace závislostí**
```bash
npm install
```

3. **Nastavení prostředí**
```bash
cp .env.example .env
```

Upravte `.env` soubor podle vašich potřeb:
```env
DATABASE_URL="file:./dev.db"
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# Email konfigurace
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"
```

4. **Inicializace databáze**
```bash
npx prisma generate
npx prisma db push
npm run db:seed
```

5. **Spuštění aplikace**
```bash
npm run dev
```

Aplikace bude dostupná na [http://localhost:3000](http://localhost:3000)

## Výchozí přihlašovací údaje

Po spuštění seed scriptu můžete použít:
- **Email**: <EMAIL>
- **Heslo**: admin123

## Struktura projektu

```
rokitbox/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── api/            # API routes
│   │   ├── auth/           # Autentifikační stránky
│   │   └── dashboard/      # Hlavní aplikace
│   ├── components/         # React komponenty
│   │   ├── ui/            # UI komponenty
│   │   └── layout/        # Layout komponenty
│   ├── lib/               # Utility funkce
│   └── types/             # TypeScript typy
├── prisma/                # Databázové schéma a migrace
└── public/               # Statické soubory
```

## API Endpoints

### Zařízení
- `GET /api/devices` - Seznam zařízení
- `POST /api/devices` - Vytvoření zařízení
- `GET /api/devices/[id]` - Detail zařízení
- `PUT /api/devices/[id]` - Aktualizace zařízení
- `DELETE /api/devices/[id]` - Smazání zařízení

### Admin (pouze pro administrátory)
- `GET /api/admin/users` - Seznam uživatelů
- `POST /api/admin/users` - Vytvoření uživatele
- `PUT /api/admin/users/[id]` - Aktualizace uživatele
- `DELETE /api/admin/users/[id]` - Smazání uživatele

## Databázové schéma

### Organizace
- Oddělené prostředí pro každou organizaci
- Vlastní uživatelé a zařízení

### Uživatelé
- Role: ADMIN, USER
- Autentifikace pomocí email/heslo
- Vazba na organizaci

### Zařízení
- Typy: COMPUTER, LAPTOP, MONITOR, PRINTER, atd.
- Stavy: ACTIVE, INACTIVE, MAINTENANCE, RETIRED
- Kompletní metadata (značka, model, SN, umístění, atd.)

## Vývoj

### Užitečné příkazy

```bash
# Vývoj
npm run dev

# Build
npm run build

# Databáze
npx prisma studio          # Databázový prohlížeč
npx prisma generate         # Generování klienta
npx prisma db push          # Synchronizace schématu
npm run db:seed            # Naplnění testovacími daty

# Linting
npm run lint
```

### Přidání nových funkcí

1. Vytvořte databázové schéma v `prisma/schema.prisma`
2. Spusťte `npx prisma db push`
3. Vytvořte API routes v `src/app/api/`
4. Implementujte frontend komponenty
5. Aktualizujte typy v `src/types/`

## Produkční nasazení

1. **Nastavte produkční databázi** (PostgreSQL doporučeno)
2. **Aktualizujte environment variables**
3. **Spusťte build**
```bash
npm run build
npm start
```

## Bezpečnost

- Všechny API routes jsou chráněny autentifikací
- Multi-tenant izolace na úrovni databáze
- CSRF ochrana pomocí NextAuth.js
- Validace vstupů na frontend i backend

## Podpora

Pro podporu a hlášení chyb kontaktujte vývojový tým.

## Licence

Proprietární software - RokIT Partner s.r.o.
